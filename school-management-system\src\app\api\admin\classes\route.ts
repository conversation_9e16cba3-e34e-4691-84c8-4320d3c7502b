import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';
import { hasPermission } from '@/lib/rbac';
import { z } from 'zod';

// Validation schema for class data
const ClassSchema = z.object({
  name: z.string().min(1, 'Class name is required'),
  sectionId: z.number().min(1, 'Section is required'),
  teacherId: z.number().optional(),
  capacity: z.number().min(1, 'Capacity must be at least 1').optional(),
  academicYear: z.string().optional(),
  isActive: z.boolean().default(true),
});

// GET /api/admin/classes - List all classes
export async function GET(request: NextRequest) {
  try {
    // const session = await getServerSession(authOptions);
    // if (!session?.user || !hasPermission(session.user.role, 'classes:read')) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    // }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const isActive = searchParams.get('isActive');

    // Fetch all classes with sections
    const classes = await prisma.class.findMany({
      include: {
        sections: true,
        teacher: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
      orderBy: { name: 'asc' },
    });
    
    // Apply filters
    let filteredClasses = classes;
    
    if (search) {
      filteredClasses = classes.filter(classItem => 
        classItem.name.toLowerCase().includes(search.toLowerCase())
      );
    }

    // Apply pagination
    const total = filteredClasses.length;
    const totalPages = Math.ceil(total / limit);
    
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedClasses = filteredClasses.slice(startIndex, endIndex);

    return NextResponse.json({
      classes: paginatedClasses,
      pagination: {
        page,
        limit,
        total,
        totalPages,
      },
    });
  } catch (error) {
    console.error('Error fetching classes:', error);
    return NextResponse.json(
      { error: 'Failed to fetch classes' },
      { status: 500 }
    );
  }
}

// POST /api/admin/classes - Create new class
export async function POST(request: NextRequest) {
  try {
    // const session = await getServerSession(authOptions);
    // if (!session?.user || !hasPermission(session.user.role, 'classes:write')) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    // }

    const body = await request.json();
    const validatedData = ClassSchema.parse(body);

    // Check if section exists
    const section = await prisma.section.findUnique({
      where: { id: validatedData.sectionId },
    });

    if (!section) {
      return NextResponse.json(
        { error: 'Section not found' },
        { status: 400 }
      );
    }

    // Check if class with same name and section already exists
    const existingClass = await prisma.class.findFirst({
      where: {
        name: validatedData.name,
        sectionId: validatedData.sectionId,
      },
    });

    if (existingClass) {
      return NextResponse.json(
        { error: 'Class with this name already exists in the selected section' },
        { status: 400 }
      );
    }

    // Check if teacher exists (if provided)
    if (validatedData.teacherId) {
      const teacher = await prisma.teacher.findUnique({
        where: { id: validatedData.teacherId },
      });

      if (!teacher) {
        return NextResponse.json(
          { error: 'Teacher not found' },
          { status: 400 }
        );
      }
    }

    // Create class
    const newClass = await prisma.class.create({
      data: validatedData,
      include: {
        section: true,
        teacher: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    return NextResponse.json(
      { 
        message: 'Class created successfully',
        class: newClass,
      },
      { status: 201 }
    );
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }
    console.error('Error creating class:', error);
    return NextResponse.json(
      { error: 'Failed to create class' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/classes - Update class
export async function PUT(request: NextRequest) {
  try {
    // const session = await getServerSession(authOptions);
    // if (!session?.user || !hasPermission(session.user.role, 'classes:write')) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    // }

    const body = await request.json();
    const { id, ...updateData } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'Class ID is required' },
        { status: 400 }
      );
    }

    const validatedData = ClassSchema.partial().parse(updateData);

    // Check if class exists
    const existingClass = await prisma.class.findUnique({
      where: { id: parseInt(id) },
    });

    if (!existingClass) {
      return NextResponse.json(
        { error: 'Class not found' },
        { status: 404 }
      );
    }

    // Check if section exists (if being updated)
    if (validatedData.sectionId) {
      const section = await prisma.section.findUnique({
        where: { id: validatedData.sectionId },
      });

      if (!section) {
        return NextResponse.json(
          { error: 'Section not found' },
          { status: 400 }
        );
      }
    }

    // Check if teacher exists (if being updated)
    if (validatedData.teacherId) {
      const teacher = await prisma.teacher.findUnique({
        where: { id: validatedData.teacherId },
      });

      if (!teacher) {
        return NextResponse.json(
          { error: 'Teacher not found' },
          { status: 400 }
        );
      }
    }

    // Update class
    const updatedClass = await prisma.class.update({
      where: { id: parseInt(id) },
      data: validatedData,
      include: {
        section: true,
        teacher: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    return NextResponse.json({
      message: 'Class updated successfully',
      class: updatedClass,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }
    console.error('Error updating class:', error);
    return NextResponse.json(
      { error: 'Failed to update class' },
      { status: 500 }
    );
  }
}
