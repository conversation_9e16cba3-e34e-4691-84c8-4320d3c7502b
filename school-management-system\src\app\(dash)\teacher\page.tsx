'use client'

import { useSession } from 'next-auth/react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import DashboardLayout from '@/components/layout/dashboard-layout'
import { 
  Users, 
  BookOpen, 
  Calendar,
  BarChart3,
  ClipboardList,
  Award,
  Clock,
  TrendingUp,
  FileText,
  Bell
} from 'lucide-react'

const teacherNavigation = [
  { name: 'Dashboard', href: '/teacher', icon: BarChart3 },
  { name: 'My Classes', href: '/teacher/classes', icon: BookOpen },
  { name: 'Attendance', href: '/teacher/attendance', icon: ClipboardList },
  { name: 'Marks', href: '/teacher/marks', icon: Award },
  { name: 'Reports', href: '/teacher/reports', icon: FileText },
  { name: 'Schedule', href: '/teacher/schedule', icon: Clock },
]

export default function TeacherDashboard() {
  const { data: session } = useSession()

  // Mock data - in real app, this would come from API
  const stats = {
    totalClasses: 4,
    totalStudents: 120,
    averageAttendance: 95.2,
    averageMarks: 82.5,
    pendingTasks: 3,
    upcomingExams: 2
  }

  const assignedClasses = [
    {
      name: 'Grade 8A',
      subject: 'Mathematics',
      students: 30,
      attendance: 96.7,
      nextClass: 'Today, 2:00 PM'
    },
    {
      name: 'Grade 8B',
      subject: 'Mathematics',
      students: 28,
      attendance: 94.1,
      nextClass: 'Tomorrow, 9:00 AM'
    },
    {
      name: 'Grade 9A',
      subject: 'Physics',
      students: 32,
      attendance: 97.2,
      nextClass: 'Today, 4:00 PM'
    },
    {
      name: 'Grade 9B',
      subject: 'Physics',
      students: 30,
      attendance: 93.8,
      nextClass: 'Tomorrow, 11:00 AM'
    }
  ]

  const quickActions = [
    {
      title: 'Take Attendance',
      description: 'Mark today\'s attendance',
      icon: ClipboardList,
      href: '/teacher/attendance',
      color: 'bg-green-500'
    },
    {
      title: 'Enter Marks',
      description: 'Upload exam results',
      icon: Award,
      href: '/teacher/marks',
      color: 'bg-blue-500'
    },
    {
      title: 'Generate Report',
      description: 'Create class reports',
      icon: FileText,
      href: '/teacher/reports',
      color: 'bg-purple-500'
    },
    {
      title: 'View Schedule',
      description: 'Check your timetable',
      icon: Clock,
      href: '/teacher/schedule',
      color: 'bg-orange-500'
    }
  ]

  return (
    <DashboardLayout title="Teacher Dashboard" navigation={teacherNavigation}>
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="bg-white rounded-lg border p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Welcome back, {session?.user?.firstName || 'Teacher'}!
          </h2>
          <p className="text-gray-600">
            Here's your teaching overview and quick actions for today.
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Classes</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalClasses}</div>
              <p className="text-xs text-muted-foreground">
                Assigned classes
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Students</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalStudents}</div>
              <p className="text-xs text-muted-foreground">
                Across all classes
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average Attendance</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.averageAttendance}%</div>
              <p className="text-xs text-muted-foreground">
                This week
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average Marks</CardTitle>
              <Award className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.averageMarks}%</div>
              <p className="text-xs text-muted-foreground">
                Current term
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Tasks</CardTitle>
              <Bell className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.pendingTasks}</div>
              <p className="text-xs text-muted-foreground">
                Require attention
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Upcoming Exams</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.upcomingExams}</div>
              <p className="text-xs text-muted-foreground">
                This week
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Assigned Classes */}
        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">My Classes</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {assignedClasses.map((classInfo) => (
              <Card key={classInfo.name} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardHeader>
                  <CardTitle className="text-lg">{classInfo.name}</CardTitle>
                  <CardDescription>{classInfo.subject}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Students:</span>
                      <span className="font-medium">{classInfo.students}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Attendance:</span>
                      <span className="font-medium text-green-600">{classInfo.attendance}%</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Next Class:</span>
                      <span className="font-medium">{classInfo.nextClass}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action) => (
              <Card key={action.title} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${action.color}`}>
                      <action.icon className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h4 className="font-medium text-sm">{action.title}</h4>
                      <p className="text-xs text-gray-500">{action.description}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Today's Schedule */}
        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Today's Schedule</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <div>
                  <p className="font-medium">Grade 8A - Mathematics</p>
                  <p className="text-sm text-gray-600">Room 101</p>
                </div>
              </div>
              <span className="text-sm font-medium">9:00 AM - 10:00 AM</span>
            </div>
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <div>
                  <p className="font-medium">Grade 9A - Physics</p>
                  <p className="text-sm text-gray-600">Lab 2</p>
                </div>
              </div>
              <span className="text-sm font-medium">2:00 PM - 3:00 PM</span>
            </div>
            <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                <div>
                  <p className="font-medium">Grade 8B - Mathematics</p>
                  <p className="text-sm text-gray-600">Room 102</p>
                </div>
              </div>
              <span className="text-sm font-medium">4:00 PM - 5:00 PM</span>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
