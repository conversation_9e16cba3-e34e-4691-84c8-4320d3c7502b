{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\r\n\r\nconst globalForPrisma = globalThis as unknown as {\r\n  prisma: PrismaClient | undefined\r\n}\r\n\r\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\r\n\r\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6IAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\r\nimport Credential<PERSON><PERSON>rovider from 'next-auth/providers/credentials'\r\nimport bcrypt from 'bcryptjs'\r\nimport { prisma } from './db'\r\n\r\nexport const authOptions: NextAuthOptions = {\r\n  providers: [\r\n    CredentialsProvider({\r\n      name: 'credentials',\r\n      credentials: {\r\n        email: { label: 'Email', type: 'email' },\r\n        password: { label: 'Password', type: 'password' }\r\n      },\r\n      async authorize(credentials) {\r\n        if (!credentials?.email || !credentials?.password) {\r\n          return null\r\n        }\r\n\r\n        try {\r\n          const user = await prisma.user.findUnique({\r\n            where: {\r\n              email: credentials.email\r\n            }\r\n          })\r\n\r\n          if (!user || !user.hashedPassword) {\r\n            return null\r\n          }\r\n\r\n          const isCorrectPassword = await bcrypt.compare(\r\n            credentials.password,\r\n            user.hashedPassword\r\n          )\r\n\r\n          if (!isCorrectPassword) {\r\n            return null\r\n          }\r\n\r\n          return {\r\n            id: user.id,\r\n            email: user.email,\r\n            name: `${user.firstName} ${user.lastName}`,\r\n            role: user.role,\r\n            firstName: user.firstName,\r\n            lastName: user.lastName\r\n          }\r\n        } catch (error) {\r\n          console.error('Auth error:', error)\r\n          return null\r\n        }\r\n      }\r\n    })\r\n  ],\r\n  session: {\r\n    strategy: 'jwt',\r\n    maxAge: 24 * 60 * 60, // 24 hours\r\n  },\r\n  callbacks: {\r\n    async jwt({ token, user }) {\r\n      if (user) {\r\n        token.role = user.role\r\n        token.firstName = user.firstName\r\n        token.lastName = user.lastName\r\n      }\r\n      return token\r\n    },\r\n    async session({ session, token }) {\r\n      if (token) {\r\n        session.user.id = token.sub!\r\n        session.user.role = token.role as string\r\n        session.user.firstName = token.firstName as string\r\n        session.user.lastName = token.lastName as string\r\n      }\r\n      return session\r\n    }\r\n  },\r\n  pages: {\r\n    signIn: '/login',\r\n    error: '/login'\r\n  },\r\n  secret: process.env.NEXTAUTH_SECRET\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,IAAA,mTAAmB,EAAC;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,IAAI;oBACF,MAAM,OAAO,MAAM,8JAAM,CAAC,IAAI,CAAC,UAAU,CAAC;wBACxC,OAAO;4BACL,OAAO,YAAY,KAAK;wBAC1B;oBACF;oBAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,cAAc,EAAE;wBACjC,OAAO;oBACT;oBAEA,MAAM,oBAAoB,MAAM,qOAAM,CAAC,OAAO,CAC5C,YAAY,QAAQ,EACpB,KAAK,cAAc;oBAGrB,IAAI,CAAC,mBAAmB;wBACtB,OAAO;oBACT;oBAEA,OAAO;wBACL,IAAI,KAAK,EAAE;wBACX,OAAO,KAAK,KAAK;wBACjB,MAAM,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;wBAC1C,MAAM,KAAK,IAAI;wBACf,WAAW,KAAK,SAAS;wBACzB,UAAU,KAAK,QAAQ;oBACzB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO;gBACT;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK;IACpB;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,SAAS,GAAG,KAAK,SAAS;gBAChC,MAAM,QAAQ,GAAG,KAAK,QAAQ;YAChC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,SAAS,GAAG,MAAM,SAAS;gBACxC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;YACxC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,OAAO;IACT;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/app/api/student/attendance/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\r\nimport { getServerSession } from 'next-auth';\r\nimport { authOptions } from '@/lib/auth';\r\nimport { prisma } from '@/lib/db';\r\nimport { hasPermission } from '@/lib/rbac';\r\n\r\n// GET /api/student/attendance - Get student's attendance records\r\nexport async function GET(request: NextRequest) {\r\n  try {\r\n    const session = await getServerSession(authOptions);\r\n    if (!session?.user || session.user.role !== 'STUDENT') {\r\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\r\n    }\r\n\r\n    const { searchParams } = new URL(request.url);\r\n    const startDate = searchParams.get('startDate');\r\n    const endDate = searchParams.get('endDate');\r\n    const page = parseInt(searchParams.get('page') || '1');\r\n    const limit = parseInt(searchParams.get('limit') || '10');\r\n\r\n    const skip = (page - 1) * limit;\r\n\r\n    // Build where clause\r\n    const where: any = {};\r\n    \r\n    if (session.user.role === 'STUDENT') {\r\n      // Get student ID from the database\r\n      const student = await prisma.student.findUnique({\r\n        where: { userId: session.user.id }\r\n      });\r\n      \r\n      if (!student) {\r\n        return NextResponse.json(\r\n          { error: 'Student profile not found' },\r\n          { status: 404 }\r\n        );\r\n      }\r\n      \r\n      where.studentId = student.id;\r\n    }\r\n\r\n    if (startDate && endDate) {\r\n      where.date = {\r\n        gte: new Date(startDate),\r\n        lte: new Date(endDate),\r\n      };\r\n    }\r\n\r\n    // Get attendance records with pagination\r\n    const [attendanceRecords, total] = await Promise.all([\r\n      prisma.attendance.findMany({\r\n        where,\r\n        skip,\r\n        take: limit,\r\n        orderBy: { date: 'desc' },\r\n        include: {\r\n          class: {\r\n            select: {\r\n              id: true,\r\n              name: true,\r\n              section: {\r\n                select: {\r\n                  name: true,\r\n                },\r\n              },\r\n            },\r\n          },\r\n        },\r\n      }),\r\n      prisma.attendance.count({ where }),\r\n    ]);\r\n\r\n    // Get student ID for statistics\r\n    const student = await prisma.student.findUnique({\r\n      where: { userId: session.user.id }\r\n    });\r\n    \r\n    if (!student) {\r\n      return NextResponse.json(\r\n        { error: 'Student profile not found' },\r\n        { status: 404 }\r\n      );\r\n    }\r\n\r\n    // Calculate attendance statistics\r\n    const totalRecords = await prisma.attendance.count({\r\n      where: {\r\n        studentId: student.id,\r\n        ...(startDate && endDate && {\r\n          date: {\r\n            gte: new Date(startDate),\r\n            lte: new Date(endDate),\r\n          },\r\n        }),\r\n      },\r\n    });\r\n\r\n    const presentCount = await prisma.attendance.count({\r\n      where: {\r\n        studentId: student.id,\r\n        status: 'PRESENT',\r\n        ...(startDate && endDate && {\r\n          date: {\r\n            gte: new Date(startDate),\r\n            lte: new Date(endDate),\r\n          },\r\n        }),\r\n      },\r\n    });\r\n\r\n    const absentCount = await prisma.attendance.count({\r\n      where: {\r\n        studentId: student.id,\r\n        status: 'ABSENT',\r\n        ...(startDate && endDate && {\r\n          date: {\r\n            gte: new Date(startDate),\r\n            lte: new Date(endDate),\r\n          },\r\n        }),\r\n      },\r\n    });\r\n\r\n    const lateCount = await prisma.attendance.count({\r\n      where: {\r\n        studentId: student.id,\r\n        status: 'LATE',\r\n        ...(startDate && endDate && {\r\n          date: {\r\n            gte: new Date(startDate),\r\n            lte: new Date(endDate),\r\n          },\r\n        }),\r\n      },\r\n    });\r\n\r\n    const halfDayCount = await prisma.attendance.count({\r\n      where: {\r\n        studentId: student.id,\r\n        status: 'HALF_DAY',\r\n        ...(startDate && endDate && {\r\n          date: {\r\n            gte: new Date(startDate),\r\n            lte: new Date(endDate),\r\n          },\r\n        }),\r\n      },\r\n    });\r\n\r\n    const totalPages = Math.ceil(total / limit);\r\n\r\n    const attendancePercentage = totalRecords > 0 ? (presentCount / totalRecords) * 100 : 0;\r\n\r\n    return NextResponse.json({\r\n      attendanceRecords,\r\n      statistics: {\r\n        total: totalRecords,\r\n        present: presentCount,\r\n        absent: absentCount,\r\n        late: lateCount,\r\n        halfDay: halfDayCount,\r\n        percentage: Math.round(attendancePercentage * 100) / 100,\r\n      },\r\n      pagination: {\r\n        page,\r\n        limit,\r\n        total,\r\n        totalPages,\r\n      },\r\n    });\r\n  } catch (error) {\r\n    console.error('Error fetching student attendance:', error);\r\n    return NextResponse.json(\r\n      { error: 'Failed to fetch attendance' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAIO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,IAAA,ySAAgB,EAAC,qKAAW;QAClD,IAAI,CAAC,SAAS,QAAQ,QAAQ,IAAI,CAAC,IAAI,KAAK,WAAW;YACrD,OAAO,iSAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,YAAY,aAAa,GAAG,CAAC;QACnC,MAAM,UAAU,aAAa,GAAG,CAAC;QACjC,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QAEpD,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAE1B,qBAAqB;QACrB,MAAM,QAAa,CAAC;QAEpB,IAAI,QAAQ,IAAI,CAAC,IAAI,KAAK,WAAW;YACnC,mCAAmC;YACnC,MAAM,UAAU,MAAM,8JAAM,CAAC,OAAO,CAAC,UAAU,CAAC;gBAC9C,OAAO;oBAAE,QAAQ,QAAQ,IAAI,CAAC,EAAE;gBAAC;YACnC;YAEA,IAAI,CAAC,SAAS;gBACZ,OAAO,iSAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAA4B,GACrC;oBAAE,QAAQ;gBAAI;YAElB;YAEA,MAAM,SAAS,GAAG,QAAQ,EAAE;QAC9B;QAEA,IAAI,aAAa,SAAS;YACxB,MAAM,IAAI,GAAG;gBACX,KAAK,IAAI,KAAK;gBACd,KAAK,IAAI,KAAK;YAChB;QACF;QAEA,yCAAyC;QACzC,MAAM,CAAC,mBAAmB,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;YACnD,8JAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;gBACzB;gBACA;gBACA,MAAM;gBACN,SAAS;oBAAE,MAAM;gBAAO;gBACxB,SAAS;oBACP,OAAO;wBACL,QAAQ;4BACN,IAAI;4BACJ,MAAM;4BACN,SAAS;gCACP,QAAQ;oCACN,MAAM;gCACR;4BACF;wBACF;oBACF;gBACF;YACF;YACA,8JAAM,CAAC,UAAU,CAAC,KAAK,CAAC;gBAAE;YAAM;SACjC;QAED,gCAAgC;QAChC,MAAM,UAAU,MAAM,8JAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAC9C,OAAO;gBAAE,QAAQ,QAAQ,IAAI,CAAC,EAAE;YAAC;QACnC;QAEA,IAAI,CAAC,SAAS;YACZ,OAAO,iSAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA4B,GACrC;gBAAE,QAAQ;YAAI;QAElB;QAEA,kCAAkC;QAClC,MAAM,eAAe,MAAM,8JAAM,CAAC,UAAU,CAAC,KAAK,CAAC;YACjD,OAAO;gBACL,WAAW,QAAQ,EAAE;gBACrB,GAAI,aAAa,WAAW;oBAC1B,MAAM;wBACJ,KAAK,IAAI,KAAK;wBACd,KAAK,IAAI,KAAK;oBAChB;gBACF,CAAC;YACH;QACF;QAEA,MAAM,eAAe,MAAM,8JAAM,CAAC,UAAU,CAAC,KAAK,CAAC;YACjD,OAAO;gBACL,WAAW,QAAQ,EAAE;gBACrB,QAAQ;gBACR,GAAI,aAAa,WAAW;oBAC1B,MAAM;wBACJ,KAAK,IAAI,KAAK;wBACd,KAAK,IAAI,KAAK;oBAChB;gBACF,CAAC;YACH;QACF;QAEA,MAAM,cAAc,MAAM,8JAAM,CAAC,UAAU,CAAC,KAAK,CAAC;YAChD,OAAO;gBACL,WAAW,QAAQ,EAAE;gBACrB,QAAQ;gBACR,GAAI,aAAa,WAAW;oBAC1B,MAAM;wBACJ,KAAK,IAAI,KAAK;wBACd,KAAK,IAAI,KAAK;oBAChB;gBACF,CAAC;YACH;QACF;QAEA,MAAM,YAAY,MAAM,8JAAM,CAAC,UAAU,CAAC,KAAK,CAAC;YAC9C,OAAO;gBACL,WAAW,QAAQ,EAAE;gBACrB,QAAQ;gBACR,GAAI,aAAa,WAAW;oBAC1B,MAAM;wBACJ,KAAK,IAAI,KAAK;wBACd,KAAK,IAAI,KAAK;oBAChB;gBACF,CAAC;YACH;QACF;QAEA,MAAM,eAAe,MAAM,8JAAM,CAAC,UAAU,CAAC,KAAK,CAAC;YACjD,OAAO;gBACL,WAAW,QAAQ,EAAE;gBACrB,QAAQ;gBACR,GAAI,aAAa,WAAW;oBAC1B,MAAM;wBACJ,KAAK,IAAI,KAAK;wBACd,KAAK,IAAI,KAAK;oBAChB;gBACF,CAAC;YACH;QACF;QAEA,MAAM,aAAa,KAAK,IAAI,CAAC,QAAQ;QAErC,MAAM,uBAAuB,eAAe,IAAI,AAAC,eAAe,eAAgB,MAAM;QAEtF,OAAO,iSAAY,CAAC,IAAI,CAAC;YACvB;YACA,YAAY;gBACV,OAAO;gBACP,SAAS;gBACT,QAAQ;gBACR,MAAM;gBACN,SAAS;gBACT,YAAY,KAAK,KAAK,CAAC,uBAAuB,OAAO;YACvD;YACA,YAAY;gBACV;gBACA;gBACA;gBACA;YACF;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO,iSAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA6B,GACtC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}