'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import TeacherForm from '@/components/teachers/teacher-form';

interface Teacher {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  dateOfBirth?: string;
  gender?: string;
  address?: string;
  qualification?: string;
  experience?: number;
  joiningDate?: string;
  salary?: number;
  isActive: boolean;
  user: {
    id: number;
    email: string;
    role: string;
  };
  classes: Array<{
    id: number;
    name: string;
    section: {
      id: number;
      name: string;
    };
  }>;
  subjects: Array<{
    id: number;
    name: string;
  }>;
}

export default function EditTeacherPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [teacher, setTeacher] = useState<Teacher | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchTeacher = async () => {
      try {
        const response = await fetch(`/api/admin/teachers/${params.id}`);
        if (!response.ok) {
          throw new Error('Failed to fetch teacher');
        }
        const data = await response.json();
        setTeacher(data.teacher);
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchTeacher();
  }, [params.id]);

  if (loading) {
    return (
      <div className="flex justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-4">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <Button onClick={() => router.push('/admin/teachers')}>
          Back to Teachers
        </Button>
      </div>
    );
  }

  if (!teacher) {
    return (
      <div className="space-y-4">
        <Alert>
          <AlertDescription>Teacher not found</AlertDescription>
        </Alert>
        <Button onClick={() => router.push('/admin/teachers')}>
          Back to Teachers
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Edit Teacher</h1>
        <p className="text-gray-600">
          Update information for {teacher.firstName} {teacher.lastName}
        </p>
      </div>
      
      <TeacherForm teacher={teacher} mode="edit" />
    </div>
  );
}
