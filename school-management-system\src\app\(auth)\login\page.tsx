'use client'

import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

export default function LoginPage() {
  const router = useRouter()

  const handleRoleLogin = (role: string) => {
    // For testing purposes, directly navigate to the appropriate dashboard
    if (role === 'ADMIN') {
      router.push('/admin')
    } else if (role === 'TEACHER') {
      router.push('/teacher')
    } else if (role === 'STUDENT') {
      router.push('/student')
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-950 p-4">
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="text-center px-4 sm:px-6">
          <CardTitle className="text-xl sm:text-2xl font-bold leading-tight">
            School Management System
          </CardTitle>
          <CardDescription className="text-sm sm:text-base mt-2">
            Select your role to continue
          </CardDescription>
        </CardHeader>
        <CardContent className="px-4 sm:px-6">
          <div className="space-y-4">
            <Button 
              onClick={() => handleRoleLogin('ADMIN')}
              className="w-full bg-blue-600 hover:bg-blue-700 min-h-[44px] text-base"
            >
              Sign in as Admin
            </Button>
            
            <Button 
              onClick={() => handleRoleLogin('TEACHER')}
              className="w-full bg-green-600 hover:bg-green-700 min-h-[44px] text-base"
            >
              Sign in as Teacher
            </Button>
            
            <Button 
              onClick={() => handleRoleLogin('STUDENT')}
              className="w-full bg-purple-600 hover:bg-purple-700 min-h-[44px] text-base"
            >
              Sign in as Student
            </Button>
          </div>

          <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <h3 className="font-semibold mb-2 text-center text-gray-900 dark:text-gray-100 text-sm sm:text-base">
              Testing Mode
            </h3>
            <p className="text-xs sm:text-sm text-center text-gray-600 dark:text-gray-400">
              Authentication is disabled for testing. Click any role button to access the dashboard.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
