'use client'

import { useState } from 'react'
import { useSession, signOut } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { ThemeToggle } from '@/components/ui/theme-toggle'
import {
  Menu,
  X,
  User,
  LogOut,
  Settings,
  Bell,
  Search,
  School,
  Plus,
  Upload,
  Download,
  Users,
  BookOpen,
  GraduationCap,
  FileText,
  BarChart3,
  Calendar,
  Home,
  Edit,
  ClipboardList,
  Award
} from 'lucide-react'

interface DashboardLayoutProps {
  children: React.ReactNode
  title: string
  navigation: {
    name: string
    href: string
    icon: string
  }[]
}

// Icon mapping object
const iconMap: Record<string, React.ComponentType<{ className?: string }>> = {
  Plus,
  Upload,
  Download,
  Users,
  BookOpen,
  GraduationCap,
  FileText,
  BarChart3,
  Calendar,
  Home,
  Settings,
  Bell,
  User,
  Edit,
  ClipboardList,
  Award
}

export default function DashboardLayout({ children, title, navigation }: DashboardLayoutProps) {
  const { data: session } = useSession()
  const router = useRouter()
  const [sidebarOpen, setSidebarOpen] = useState(false)

  const handleSignOut = async () => {
    await signOut({ callbackUrl: '/' })
  }

  const getIcon = (iconName: string) => {
    const IconComponent = iconMap[iconName]
    return IconComponent || Home // fallback to Home icon if not found
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-950">
      {/* Mobile sidebar */}
      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
        <div className="fixed inset-y-0 left-0 flex w-64 flex-col bg-white dark:bg-gray-900">
          <div className="flex h-16 items-center justify-between px-4">
            <div className="flex items-center">
              <School className="h-8 w-8 text-blue-600" />
              <span className="ml-2 text-lg font-semibold">SMS</span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSidebarOpen(false)}
            >
              <X className="h-5 w-5" />
            </Button>
          </div>
          <nav className="flex-1 space-y-1 px-2 py-4">
            {navigation.map((item) => {
              const IconComponent = getIcon(item.icon)
              return (
                <Button
                  key={item.name}
                  variant="ghost"
                  className="w-full justify-start"
                  onClick={() => {
                    router.push(item.href)
                    setSidebarOpen(false)
                  }}
                >
                  <IconComponent className="mr-3 h-5 w-5" />
                  {item.name}
                </Button>
              )
            })}
          </nav>
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
        <div className="flex flex-col flex-grow bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800">
          <div className="flex h-16 items-center px-4">
            <School className="h-8 w-8 text-blue-600" />
            <span className="ml-2 text-lg font-semibold hidden xl:inline">School Management System</span>
            <span className="ml-2 text-lg font-semibold xl:hidden">SMS</span>
          </div>
          <nav className="flex-1 space-y-1 px-2 py-4">
            {navigation.map((item) => {
              const IconComponent = getIcon(item.icon)
              return (
                <Button
                  key={item.name}
                  variant="ghost"
                  className="w-full justify-start"
                  onClick={() => router.push(item.href)}
                >
                  <IconComponent className="mr-3 h-5 w-5" />
                  {item.name}
                </Button>
              )
            })}
          </nav>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Top bar */}
        <div className="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8">
          <Button
            variant="ghost"
            size="sm"
            className="lg:hidden"
            onClick={() => setSidebarOpen(true)}
          >
            <Menu className="h-5 w-5" />
          </Button>

          <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
            <div className="relative flex flex-1">
              <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search..."
                className="block h-full w-full border-0 py-0 pl-10 pr-0 text-gray-900 dark:text-gray-100 placeholder:text-gray-400 dark:placeholder:text-gray-500 focus:ring-0 sm:text-sm bg-transparent"
              />
            </div>
          </div>

          <div className="flex items-center gap-x-4 lg:gap-x-6">
            <ThemeToggle />

            <Button variant="ghost" size="sm">
              <Bell className="h-5 w-5" />
            </Button>

            <div className="relative">
              <div className="flex items-center gap-x-3">
                <div className="text-sm hidden sm:block">
                  <p className="font-medium text-gray-900 dark:text-gray-100">
                    {session?.user?.firstName} {session?.user?.lastName}
                  </p>
                  <p className="text-gray-500 dark:text-gray-400 capitalize">
                    {session?.user?.role?.toLowerCase()}
                  </p>
                </div>
                <div className="flex items-center gap-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleSignOut}
                    className="flex items-center gap-2"
                  >
                    <LogOut className="h-4 w-4" />
                    <span className="hidden sm:inline">Sign Out</span>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="py-6">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="mb-6">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">{title}</h1>
            </div>
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}
