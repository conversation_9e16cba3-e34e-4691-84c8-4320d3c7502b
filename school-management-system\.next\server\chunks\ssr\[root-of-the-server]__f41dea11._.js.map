{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\r\n\r\nconst globalForPrisma = globalThis as unknown as {\r\n  prisma: PrismaClient | undefined\r\n}\r\n\r\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\r\n\r\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6IAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/students/student-table.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const StudentTable = registerClientReference(\n    function() { throw new Error(\"Attempted to call StudentTable() from the server but StudentTable is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/school-management-system/src/components/students/student-table.tsx <module evaluation>\",\n    \"StudentTable\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,eAAe,IAAA,yZAAuB,EAC/C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,oGACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/students/student-table.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const StudentTable = registerClientReference(\n    function() { throw new Error(\"Attempted to call StudentTable() from the server but StudentTable is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/school-management-system/src/components/students/student-table.tsx\",\n    \"StudentTable\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,eAAe,IAAA,yZAAuB,EAC/C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,gFACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/layout/dashboard-layout.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/school-management-system/src/components/layout/dashboard-layout.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/school-management-system/src/components/layout/dashboard-layout.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,yZAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAuU,GACpW,qGACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/layout/dashboard-layout.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/school-management-system/src/components/layout/dashboard-layout.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/school-management-system/src/components/layout/dashboard-layout.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,yZAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAmT,GAChV,iFACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/navigation.ts"], "sourcesContent": ["// Shared navigation configurations for different user roles\n\nexport const adminNavigation = [\n  { name: 'Dashboard', href: '/admin', icon: 'BarChart3' },\n  { name: 'Students', href: '/admin/students', icon: 'Users' },\n  { name: 'Teachers', href: '/admin/teachers', icon: 'GraduationCap' },\n  { name: 'Classes & Sections', href: '/admin/classes', icon: 'BookOpen' },\n  { name: 'Subjects', href: '/admin/subjects', icon: 'FileText' },\n  { name: 'Terms & Exams', href: '/admin/exams', icon: 'Calendar' },\n  { name: 'Attendance', href: '/admin/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/admin/marks', icon: 'Award' },\n  { name: 'Reports', href: '/admin/reports', icon: 'FileText' },\n  { name: 'Settings', href: '/admin/settings', icon: 'Settings' },\n];\n\nexport const teacherNavigation = [\n  { name: 'Dashboard', href: '/teacher', icon: 'BarChart3' },\n  { name: 'My Classes', href: '/teacher/classes', icon: 'BookOpen' },\n  { name: 'Attendance', href: '/teacher/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/teacher/marks', icon: 'Award' },\n  { name: 'Students', href: '/teacher/students', icon: 'Users' },\n  { name: 'Reports', href: '/teacher/reports', icon: 'FileText' },\n  { name: 'Profile', href: '/teacher/profile', icon: 'User' },\n];\n\nexport const studentNavigation = [\n  { name: 'Dashboard', href: '/student', icon: 'BarChart3' },\n  { name: 'My Classes', href: '/student/classes', icon: 'BookOpen' },\n  { name: 'Attendance', href: '/student/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/student/marks', icon: 'Award' },\n  { name: 'Reports', href: '/student/reports', icon: 'FileText' },\n  { name: 'Profile', href: '/student/profile', icon: 'User' },\n];"], "names": [], "mappings": "AAAA,4DAA4D;;;;;;;;;AAErD,MAAM,kBAAkB;IAC7B;QAAE,MAAM;QAAa,MAAM;QAAU,MAAM;IAAY;IACvD;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAQ;IAC3D;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAgB;IACnE;QAAE,MAAM;QAAsB,MAAM;QAAkB,MAAM;IAAW;IACvE;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAiB,MAAM;QAAgB,MAAM;IAAW;IAChE;QAAE,MAAM;QAAc,MAAM;QAAqB,MAAM;IAAgB;IACvE;QAAE,MAAM;QAAS,MAAM;QAAgB,MAAM;IAAQ;IACrD;QAAE,MAAM;QAAW,MAAM;QAAkB,MAAM;IAAW;IAC5D;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAW;CAC/D;AAEM,MAAM,oBAAoB;IAC/B;QAAE,MAAM;QAAa,MAAM;QAAY,MAAM;IAAY;IACzD;QAAE,MAAM;QAAc,MAAM;QAAoB,MAAM;IAAW;IACjE;QAAE,MAAM;QAAc,MAAM;QAAuB,MAAM;IAAgB;IACzE;QAAE,MAAM;QAAS,MAAM;QAAkB,MAAM;IAAQ;IACvD;QAAE,MAAM;QAAY,MAAM;QAAqB,MAAM;IAAQ;IAC7D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAO;CAC3D;AAEM,MAAM,oBAAoB;IAC/B;QAAE,MAAM;QAAa,MAAM;QAAY,MAAM;IAAY;IACzD;QAAE,MAAM;QAAc,MAAM;QAAoB,MAAM;IAAW;IACjE;QAAE,MAAM;QAAc,MAAM;QAAuB,MAAM;IAAgB;IACzE;QAAE,MAAM;QAAS,MAAM;QAAkB,MAAM;IAAQ;IACvD;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAO;CAC3D", "debugId": null}}, {"offset": {"line": 240, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sQAAO,EAAC,IAAA,gOAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 255, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h3\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <p\r\n    ref={ref}\r\n    className={cn(\"text-sm text-gray-600 dark:text-gray-400\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,oWAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,+HACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,oWAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,oWAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAA<PERSON>;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,oWAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EAAC,4CAA4C;QACzD,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,oWAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QAAI,KAAK;QAAK,WAAW,IAAA,2JAAE,EAAC,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,oWAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 340, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700\",\r\n        destructive:\r\n          \"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700\",\r\n        outline:\r\n          \"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100\",\r\n        secondary:\r\n          \"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700\",\r\n        ghost: \"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100\",\r\n        link: \"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,IAAA,oRAAG,EACxB,uQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,oWAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,+UAAI,GAAG;IAC9B,qBACE,+XAAC;QACC,WAAW,IAAA,2JAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 400, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/app/%28dash%29/admin/students/page.tsx"], "sourcesContent": ["import { Suspense } from 'react';\nimport { getServerSession } from 'next-auth';\nimport { redirect } from 'next/navigation';\nimport { authOptions } from '@/lib/auth';\nimport { prisma as db } from '@/lib/db';\nimport { hasPermission } from '@/lib/rbac';\nimport { StudentTable } from '@/components/students/student-table';\nimport DashboardLayout from '@/components/layout/dashboard-layout';\nimport { adminNavigation } from '@/lib/navigation';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Plus, Upload, Download } from 'lucide-react';\nimport Link from 'next/link';\nimport { Loader2 } from 'lucide-react';\n\ninterface StudentsPageProps {\n  searchParams: {\n    search?: string;\n    classId?: string;\n    gender?: string;\n    page?: string;\n  };\n}\n\nasync function StudentsPageContent({ searchParams }: StudentsPageProps) {\n  // Temporarily bypass authentication for testing\n  // const session = await getServerSession(authOptions);\n  \n  // if (!session?.user) {\n  //   redirect('/login');\n  // }\n\n  // if (!hasPermission(session.user.role as any, 'students:read')) {\n  //   redirect('/unauthorized');\n  // }\n\n  const { search, classId, gender, page = '1' } = searchParams;\n  const currentPage = parseInt(page);\n  const limit = 10;\n\n  // Build where clause\n  const where: any = {};\n\n  if (search) {\n    where.OR = [\n      { user: { firstName: { contains: search, mode: 'insensitive' } } },\n      { user: { lastName: { contains: search, mode: 'insensitive' } } },\n      { user: { email: { contains: search, mode: 'insensitive' } } },\n    ];\n  }\n\n  if (classId) {\n    where.currentClassId = classId;\n  }\n\n  if (gender) {\n    where.gender = gender;\n  }\n\n  // Calculate pagination\n  const skip = (currentPage - 1) * limit;\n\n  // Fetch students and classes\n  const [students, totalCount, classes] = await Promise.all([\n    db.student.findMany({\n      where,\n      include: {\n        user: true,\n        currentClass: true,\n        currentSection: true,\n      },\n      skip,\n      take: limit,\n      orderBy: [\n        { user: { lastName: 'asc' } },\n        { user: { firstName: 'asc' } },\n      ],\n    }),\n    db.student.count({ where }),\n    db.class.findMany({\n      include: {\n        sections: true,\n      },\n      orderBy: [\n        { name: 'asc' },\n      ],\n    }),\n  ]);\n\n  // Calculate pagination info\n  const totalPages = Math.ceil(totalCount / limit);\n  const hasNextPage = currentPage < totalPages;\n  const hasPrevPage = currentPage > 1;\n\n  const pagination = {\n    page: currentPage,\n    limit,\n    totalCount,\n    totalPages,\n    hasNextPage,\n    hasPrevPage,\n  };\n\n  return (\n    <StudentTable \n      students={students} \n      classes={classes} \n      pagination={pagination} \n    />\n  );\n}\n\nfunction LoadingFallback() {\n  return (\n    <Card>\n      <CardContent className=\"flex items-center justify-center py-12\">\n        <div className=\"flex items-center gap-2\">\n          <Loader2 className=\"w-6 h-6 animate-spin\" />\n          <span>Loading students...</span>\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n\nexport default function StudentsPage({ searchParams }: StudentsPageProps) {\n  return (\n    <DashboardLayout \n      title=\"Students\"\n      navigation={adminNavigation}\n    >\n      <div className=\"space-y-6\">\n        <div className=\"flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0\">\n          <div>\n            <h1 className=\"text-2xl sm:text-3xl font-bold tracking-tight\">Students</h1>\n            <p className=\"text-muted-foreground\">\n              Manage student information and records\n            </p>\n          </div>\n          <div className=\"flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2\">\n            <Link href=\"/admin/students/new\" className=\"w-full sm:w-auto\">\n              <Button className=\"w-full sm:w-auto\">\n                <Plus className=\"w-4 h-4 mr-2\" />\n                <span className=\"sm:hidden\">Add Student</span>\n                <span className=\"hidden sm:inline\">Add Student</span>\n              </Button>\n            </Link>\n            <Link href=\"/admin/students/bulk\" className=\"w-full sm:w-auto\">\n              <Button variant=\"outline\" className=\"w-full sm:w-auto\">\n                <Upload className=\"w-4 h-4 mr-2\" />\n                <span className=\"sm:hidden\">Import</span>\n                <span className=\"hidden sm:inline\">Bulk Import</span>\n              </Button>\n            </Link>\n          </div>\n        </div>\n        \n        <Suspense fallback={<LoadingFallback />}>\n          <StudentsPageContent searchParams={searchParams} />\n        </Suspense>\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAIA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;;;;;;;;;;;;AAWA,eAAe,oBAAoB,EAAE,YAAY,EAAqB;IACpE,gDAAgD;IAChD,uDAAuD;IAEvD,wBAAwB;IACxB,wBAAwB;IACxB,IAAI;IAEJ,mEAAmE;IACnE,+BAA+B;IAC/B,IAAI;IAEJ,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,GAAG,EAAE,GAAG;IAChD,MAAM,cAAc,SAAS;IAC7B,MAAM,QAAQ;IAEd,qBAAqB;IACrB,MAAM,QAAa,CAAC;IAEpB,IAAI,QAAQ;QACV,MAAM,EAAE,GAAG;YACT;gBAAE,MAAM;oBAAE,WAAW;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;YAAE;YACjE;gBAAE,MAAM;oBAAE,UAAU;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;YAAE;YAChE;gBAAE,MAAM;oBAAE,OAAO;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;YAAE;SAC9D;IACH;IAEA,IAAI,SAAS;QACX,MAAM,cAAc,GAAG;IACzB;IAEA,IAAI,QAAQ;QACV,MAAM,MAAM,GAAG;IACjB;IAEA,uBAAuB;IACvB,MAAM,OAAO,CAAC,cAAc,CAAC,IAAI;IAEjC,6BAA6B;IAC7B,MAAM,CAAC,UAAU,YAAY,QAAQ,GAAG,MAAM,QAAQ,GAAG,CAAC;QACxD,4JAAE,CAAC,OAAO,CAAC,QAAQ,CAAC;YAClB;YACA,SAAS;gBACP,MAAM;gBACN,cAAc;gBACd,gBAAgB;YAClB;YACA;YACA,MAAM;YACN,SAAS;gBACP;oBAAE,MAAM;wBAAE,UAAU;oBAAM;gBAAE;gBAC5B;oBAAE,MAAM;wBAAE,WAAW;oBAAM;gBAAE;aAC9B;QACH;QACA,4JAAE,CAAC,OAAO,CAAC,KAAK,CAAC;YAAE;QAAM;QACzB,4JAAE,CAAC,KAAK,CAAC,QAAQ,CAAC;YAChB,SAAS;gBACP,UAAU;YACZ;YACA,SAAS;gBACP;oBAAE,MAAM;gBAAM;aACf;QACH;KACD;IAED,4BAA4B;IAC5B,MAAM,aAAa,KAAK,IAAI,CAAC,aAAa;IAC1C,MAAM,cAAc,cAAc;IAClC,MAAM,cAAc,cAAc;IAElC,MAAM,aAAa;QACjB,MAAM;QACN;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,+XAAC,oMAAY;QACX,UAAU;QACV,SAAS;QACT,YAAY;;;;;;AAGlB;AAEA,SAAS;IACP,qBACE,+XAAC,0KAAI;kBACH,cAAA,+XAAC,iLAAW;YAAC,WAAU;sBACrB,cAAA,+XAAC;gBAAI,WAAU;;kCACb,+XAAC,kVAAO;wBAAC,WAAU;;;;;;kCACnB,+XAAC;kCAAK;;;;;;;;;;;;;;;;;;;;;;AAKhB;AAEe,SAAS,aAAa,EAAE,YAAY,EAAqB;IACtE,qBACE,+XAAC,gMAAe;QACd,OAAM;QACN,YAAY,6KAAe;kBAE3B,cAAA,+XAAC;YAAI,WAAU;;8BACb,+XAAC;oBAAI,WAAU;;sCACb,+XAAC;;8CACC,+XAAC;oCAAG,WAAU;8CAAgD;;;;;;8CAC9D,+XAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAIvC,+XAAC;4BAAI,WAAU;;8CACb,+XAAC,wTAAI;oCAAC,MAAK;oCAAsB,WAAU;8CACzC,cAAA,+XAAC,8KAAM;wCAAC,WAAU;;0DAChB,+XAAC,gUAAI;gDAAC,WAAU;;;;;;0DAChB,+XAAC;gDAAK,WAAU;0DAAY;;;;;;0DAC5B,+XAAC;gDAAK,WAAU;0DAAmB;;;;;;;;;;;;;;;;;8CAGvC,+XAAC,wTAAI;oCAAC,MAAK;oCAAuB,WAAU;8CAC1C,cAAA,+XAAC,8KAAM;wCAAC,SAAQ;wCAAU,WAAU;;0DAClC,+XAAC,sUAAM;gDAAC,WAAU;;;;;;0DAClB,+XAAC;gDAAK,WAAU;0DAAY;;;;;;0DAC5B,+XAAC;gDAAK,WAAU;0DAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAM3C,+XAAC,kWAAQ;oBAAC,wBAAU,+XAAC;;;;;8BACnB,cAAA,+XAAC;wBAAoB,cAAc;;;;;;;;;;;;;;;;;;;;;;AAK7C", "debugId": null}}]}