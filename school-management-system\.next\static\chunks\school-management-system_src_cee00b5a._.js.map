{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,yQAAO,EAAC,IAAA,mOAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h3\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <p\r\n    ref={ref}\r\n    className={cn(\"text-sm text-gray-600 dark:text-gray-400\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,4TAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,+HACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,4TAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,4TAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,sDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,4TAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,4CAA4C;QACzD,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,4TAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QAAI,KAAK;QAAK,WAAW,IAAA,8JAAE,EAAC,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,4TAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700\",\r\n        destructive:\r\n          \"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700\",\r\n        outline:\r\n          \"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100\",\r\n        secondary:\r\n          \"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700\",\r\n        ghost: \"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100\",\r\n        link: \"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,IAAA,uRAAG,EACxB,uQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,4TAAgB,MAC7B,QAA0D;QAAzD,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO;IACtD,MAAM,OAAO,UAAU,kVAAI,GAAG;IAC9B,qBACE,8UAAC;QACC,WAAW,IAAA,8JAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 218, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["'use client'\n\nimport * as React from 'react'\nimport * as DropdownMenuPrimitive from '@radix-ui/react-dropdown-menu'\nimport { Check, ChevronRight, Circle } from 'lucide-react'\nimport { cn } from '@/lib/utils'\n\nconst DropdownMenu = DropdownMenuPrimitive.Root\n\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\n\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\n\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\n\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\n\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      'flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\n  </DropdownMenuPrimitive.SubTrigger>\n))\nDropdownMenuSubTrigger.displayName = DropdownMenuPrimitive.SubTrigger.displayName\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      'z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuSubContent.displayName = DropdownMenuPrimitive.SubContent.displayName\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        'z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',\n        className\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n))\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      'relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      'relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.CheckboxItem>\n))\nDropdownMenuCheckboxItem.displayName = DropdownMenuPrimitive.CheckboxItem.displayName\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      'relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.RadioItem>\n))\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      'px-2 py-1.5 text-sm font-semibold',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn('-mx-1 my-1 h-px bg-muted', className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\n\nconst DropdownMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn('ml-auto text-xs tracking-widest opacity-60', className)}\n      {...props}\n    />\n  )\n}\nDropdownMenuShortcut.displayName = 'DropdownMenuShortcut'\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AALA;;;;;;AAOA,MAAM,eAAe,gUAA0B;AAE/C,MAAM,sBAAsB,mUAA6B;AAEzD,MAAM,oBAAoB,iUAA2B;AAErD,MAAM,qBAAqB,kUAA4B;AAEvD,MAAM,kBAAkB,+TAAyB;AAEjD,MAAM,yBAAyB,sUAAgC;AAE/D,MAAM,uCAAyB,4TAAgB,MAK7C,QAA2C;QAA1C,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO;yBACzC,8UAAC,sUAAgC;QAC/B,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,wIACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,8UAAC,+VAAY;gBAAC,WAAU;;;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAAG,sUAAgC,CAAC,WAAW;AAEjF,MAAM,uCAAyB,4TAAgB,OAG7C,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC,sUAAgC;QAC/B,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,ybACA;QAED,GAAG,KAAK;;;;;;;;AAGb,uBAAuB,WAAW,GAAG,sUAAgC,CAAC,WAAW;AAEjF,MAAM,oCAAsB,4TAAgB,OAG1C,QAA0C;QAAzC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO;yBACxC,8UAAC,kUAA4B;kBAC3B,cAAA,8UAAC,mUAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,IAAA,8JAAE,EACX,ybACA;YAED,GAAG,KAAK;;;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,mUAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,4TAAgB,OAKvC,QAAiC;QAAhC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO;yBAC/B,8UAAC,gUAA0B;QACzB,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,mOACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;;AAGb,iBAAiB,WAAW,GAAG,gUAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,4TAAgB,OAG/C,QAA6C;QAA5C,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO;yBAC3C,8UAAC,wUAAkC;QACjC,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8UAAC;gBAAK,WAAU;0BACd,cAAA,8UAAC,yUAAmC;8BAClC,cAAA,8UAAC,sUAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;;;AAGL,yBAAyB,WAAW,GAAG,wUAAkC,CAAC,WAAW;AAErF,MAAM,sCAAwB,4TAAgB,QAG5C,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAClC,8UAAC,qUAA+B;QAC9B,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,wOACA;QAED,GAAG,KAAK;;0BAET,8UAAC;gBAAK,WAAU;0BACd,cAAA,8UAAC,yUAAmC;8BAClC,cAAA,8UAAC,yUAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;;;AAGL,sBAAsB,WAAW,GAAG,qUAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,4TAAgB,QAKxC,QAAiC;QAAhC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO;yBAC/B,8UAAC,iUAA2B;QAC1B,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;;AAGb,kBAAkB,WAAW,GAAG,iUAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,4TAAgB,QAG5C,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC,qUAA+B;QAC9B,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,4BAA4B;QACzC,GAAG,KAAK;;;;;;;;AAGb,sBAAsB,WAAW,GAAG,qUAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB;QAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,8UAAC;QACC,WAAW,IAAA,8JAAE,EAAC,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;OAVM;AAWN,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 484, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/theme-toggle.tsx"], "sourcesContent": ["'use client'\n\nimport { <PERSON>, Sun, Monitor } from 'lucide-react'\nimport { Button } from './button'\nimport { useTheme } from '@/components/providers/theme-provider'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from './dropdown-menu'\n\nexport function ThemeToggle() {\n  const { theme, actualTheme, mounted, setTheme } = useTheme()\n\n  const toggleTheme = () => {\n    if (!mounted) return\n    const next = actualTheme === 'light' ? 'dark' : 'light'\n    setTheme(next)\n  }\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button\n          variant=\"ghost\"\n          size=\"icon\"\n          className=\"h-9 w-9\"\n          onClick={toggleTheme}\n          title={`Switch to ${actualTheme === 'light' ? 'dark' : 'light'} mode`}\n        >\n          <Sun className=\"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n          <Moon className=\"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n          <span className=\"sr-only\">Toggle theme</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\">\n        <DropdownMenuItem onClick={() => setTheme('light')}>\n          <Sun className=\"mr-2 h-4 w-4\" />\n          <span>Light</span>\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme('dark')}>\n          <Moon className=\"mr-2 h-4 w-4\" />\n          <span>Dark</span>\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme('system')}>\n          <Monitor className=\"mr-2 h-4 w-4\" />\n          <span>System</span>\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AAAA;AACA;AACA;AACA;;;AALA;;;;;AAYO,SAAS;;IACd,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,IAAA,qMAAQ;IAE1D,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;QACd,MAAM,OAAO,gBAAgB,UAAU,SAAS;QAChD,SAAS;IACX;IAEA,qBACE,8UAAC,iMAAY;;0BACX,8UAAC,wMAAmB;gBAAC,OAAO;0BAC1B,cAAA,8UAAC,iLAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,SAAS;oBACT,OAAO,AAAC,aAAuD,OAA3C,gBAAgB,UAAU,SAAS,SAAQ;;sCAE/D,8UAAC,gUAAG;4BAAC,WAAU;;;;;;sCACf,8UAAC,mUAAI;4BAAC,WAAU;;;;;;sCAChB,8UAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,8UAAC,wMAAmB;gBAAC,OAAM;;kCACzB,8UAAC,qMAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,8UAAC,gUAAG;gCAAC,WAAU;;;;;;0CACf,8UAAC;0CAAK;;;;;;;;;;;;kCAER,8UAAC,qMAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,8UAAC,mUAAI;gCAAC,WAAU;;;;;;0CAChB,8UAAC;0CAAK;;;;;;;;;;;;kCAER,8UAAC,qMAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,8UAAC,4UAAO;gCAAC,WAAU;;;;;;0CACnB,8UAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAKhB;GAxCgB;;QACoC,qMAAQ;;;KAD5C", "debugId": null}}, {"offset": {"line": 654, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/layout/dashboard-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useSession, signOut } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { ThemeToggle } from '@/components/ui/theme-toggle'\nimport {\n  Menu,\n  X,\n  User,\n  LogOut,\n  Settings,\n  Bell,\n  Search,\n  School,\n  Plus,\n  Upload,\n  Download,\n  Users,\n  BookOpen,\n  GraduationCap,\n  FileText,\n  BarChart3,\n  Calendar,\n  Home,\n  Edit,\n  ClipboardList,\n  Award\n} from 'lucide-react'\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n  title: string\n  navigation: {\n    name: string\n    href: string\n    icon: string\n  }[]\n}\n\n// Icon mapping object\nconst iconMap: Record<string, React.ComponentType<{ className?: string }>> = {\n  Plus,\n  Upload,\n  Download,\n  Users,\n  BookOpen,\n  GraduationCap,\n  FileText,\n  BarChart3,\n  Calendar,\n  Home,\n  Settings,\n  Bell,\n  User,\n  Edit,\n  ClipboardList,\n  Award\n}\n\nexport default function DashboardLayout({ children, title, navigation }: DashboardLayoutProps) {\n  const { data: session } = useSession()\n  const router = useRouter()\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n\n  const handleSignOut = async () => {\n    await signOut({ callbackUrl: '/' })\n  }\n\n  const getIcon = (iconName: string) => {\n    const IconComponent = iconMap[iconName]\n    return IconComponent || Home // fallback to Home icon if not found\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-950\">\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"fixed inset-y-0 left-0 flex w-64 flex-col bg-white dark:bg-gray-900\">\n          <div className=\"flex h-16 items-center justify-between px-4\">\n            <div className=\"flex items-center\">\n              <School className=\"h-8 w-8 text-blue-600\" />\n              <span className=\"ml-2 text-lg font-semibold\">SMS</span>\n            </div>\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <X className=\"h-5 w-5\" />\n            </Button>\n          </div>\n          <nav className=\"flex-1 space-y-1 px-2 py-4\">\n            {navigation.map((item) => {\n              const IconComponent = getIcon(item.icon)\n              return (\n                <Button\n                  key={item.name}\n                  variant=\"ghost\"\n                  className=\"w-full justify-start\"\n                  onClick={() => {\n                    router.push(item.href)\n                    setSidebarOpen(false)\n                  }}\n                >\n                  <IconComponent className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </Button>\n              )\n            })}\n          </nav>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex flex-col flex-grow bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800\">\n          <div className=\"flex h-16 items-center px-4\">\n            <School className=\"h-8 w-8 text-blue-600\" />\n            <span className=\"ml-2 text-lg font-semibold hidden xl:inline\">School Management System</span>\n            <span className=\"ml-2 text-lg font-semibold xl:hidden\">SMS</span>\n          </div>\n          <nav className=\"flex-1 space-y-1 px-2 py-4\">\n            {navigation.map((item) => {\n              const IconComponent = getIcon(item.icon)\n              return (\n                <Button\n                  key={item.name}\n                  variant=\"ghost\"\n                  className=\"w-full justify-start\"\n                  onClick={() => router.push(item.href)}\n                >\n                  <IconComponent className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </Button>\n              )\n            })}\n          </nav>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Top bar */}\n        <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"lg:hidden\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <Menu className=\"h-5 w-5\" />\n          </Button>\n\n          <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\n            <div className=\"relative flex flex-1\">\n              <div className=\"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3\">\n                <Search className=\"h-5 w-5 text-gray-400\" />\n              </div>\n              <input\n                type=\"text\"\n                placeholder=\"Search...\"\n                className=\"block h-full w-full border-0 py-0 pl-10 pr-0 text-gray-900 dark:text-gray-100 placeholder:text-gray-400 dark:placeholder:text-gray-500 focus:ring-0 sm:text-sm bg-transparent\"\n              />\n            </div>\n          </div>\n\n          <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\n            <ThemeToggle />\n\n            <Button variant=\"ghost\" size=\"sm\">\n              <Bell className=\"h-5 w-5\" />\n            </Button>\n\n            <div className=\"relative\">\n              <div className=\"flex items-center gap-x-3\">\n                <div className=\"text-sm hidden sm:block\">\n                  <p className=\"font-medium text-gray-900 dark:text-gray-100\">\n                    {session?.user?.firstName} {session?.user?.lastName}\n                  </p>\n                  <p className=\"text-gray-500 dark:text-gray-400 capitalize\">\n                    {session?.user?.role?.toLowerCase()}\n                  </p>\n                </div>\n                <div className=\"flex items-center gap-x-2\">\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={handleSignOut}\n                    className=\"flex items-center gap-2\"\n                  >\n                    <LogOut className=\"h-4 w-4\" />\n                    <span className=\"hidden sm:inline\">Sign Out</span>\n                  </Button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"py-6\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            <div className=\"mb-6\">\n              <h1 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">{title}</h1>\n            </div>\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;AA0CA,sBAAsB;AACtB,MAAM,UAAuE;IAC3E,MAAA,mUAAI;IACJ,QAAA,yUAAM;IACN,UAAA,+UAAQ;IACR,OAAA,sUAAK;IACL,UAAA,mVAAQ;IACR,eAAA,kWAAa;IACb,UAAA,mVAAQ;IACR,WAAA,wVAAS;IACT,UAAA,+UAAQ;IACR,MAAA,oUAAI;IACJ,UAAA,+UAAQ;IACR,MAAA,mUAAI;IACJ,MAAA,mUAAI;IACJ,MAAA,4UAAI;IACJ,eAAA,kWAAa;IACb,OAAA,sUAAK;AACP;AAEe,SAAS,gBAAgB,KAAqD;QAArD,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAwB,GAArD;QAuHnB,eAA2B,gBAG3B,oBAAA;;IAzHnB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,6SAAU;IACpC,MAAM,SAAS,IAAA,mSAAS;IACxB,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,0TAAQ,EAAC;IAE/C,MAAM,gBAAgB;QACpB,MAAM,IAAA,0SAAO,EAAC;YAAE,aAAa;QAAI;IACnC;IAEA,MAAM,UAAU,CAAC;QACf,MAAM,gBAAgB,OAAO,CAAC,SAAS;QACvC,OAAO,iBAAiB,oUAAI,CAAC,qCAAqC;;IACpE;IAEA,qBACE,8UAAC;QAAI,WAAU;;0BAEb,8UAAC;gBAAI,WAAW,AAAC,gCAAgE,OAAjC,cAAc,UAAU;;kCACtE,8UAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,8UAAC;wBAAI,WAAU;;0CACb,8UAAC;gCAAI,WAAU;;kDACb,8UAAC;wCAAI,WAAU;;0DACb,8UAAC,yUAAM;gDAAC,WAAU;;;;;;0DAClB,8UAAC;gDAAK,WAAU;0DAA6B;;;;;;;;;;;;kDAE/C,8UAAC,iLAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe;kDAE9B,cAAA,8UAAC,0TAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGjB,8UAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC;oCACf,MAAM,gBAAgB,QAAQ,KAAK,IAAI;oCACvC,qBACE,8UAAC,iLAAM;wCAEL,SAAQ;wCACR,WAAU;wCACV,SAAS;4CACP,OAAO,IAAI,CAAC,KAAK,IAAI;4CACrB,eAAe;wCACjB;;0DAEA,8UAAC;gDAAc,WAAU;;;;;;4CACxB,KAAK,IAAI;;uCATL,KAAK,IAAI;;;;;gCAYpB;;;;;;;;;;;;;;;;;;0BAMN,8UAAC;gBAAI,WAAU;0BACb,cAAA,8UAAC;oBAAI,WAAU;;sCACb,8UAAC;4BAAI,WAAU;;8CACb,8UAAC,yUAAM;oCAAC,WAAU;;;;;;8CAClB,8UAAC;oCAAK,WAAU;8CAA8C;;;;;;8CAC9D,8UAAC;oCAAK,WAAU;8CAAuC;;;;;;;;;;;;sCAEzD,8UAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,gBAAgB,QAAQ,KAAK,IAAI;gCACvC,qBACE,8UAAC,iLAAM;oCAEL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,OAAO,IAAI,CAAC,KAAK,IAAI;;sDAEpC,8UAAC;4CAAc,WAAU;;;;;;wCACxB,KAAK,IAAI;;mCANL,KAAK,IAAI;;;;;4BASpB;;;;;;;;;;;;;;;;;0BAMN,8UAAC;gBAAI,WAAU;;kCAEb,8UAAC;wBAAI,WAAU;;0CACb,8UAAC,iLAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,eAAe;0CAE9B,cAAA,8UAAC,mUAAI;oCAAC,WAAU;;;;;;;;;;;0CAGlB,8UAAC;gCAAI,WAAU;0CACb,cAAA,8UAAC;oCAAI,WAAU;;sDACb,8UAAC;4CAAI,WAAU;sDACb,cAAA,8UAAC,yUAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,8UAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;;;;;;;;;;;;0CAKhB,8UAAC;gCAAI,WAAU;;kDACb,8UAAC,+LAAW;;;;;kDAEZ,8UAAC,iLAAM;wCAAC,SAAQ;wCAAQ,MAAK;kDAC3B,cAAA,8UAAC,mUAAI;4CAAC,WAAU;;;;;;;;;;;kDAGlB,8UAAC;wCAAI,WAAU;kDACb,cAAA,8UAAC;4CAAI,WAAU;;8DACb,8UAAC;oDAAI,WAAU;;sEACb,8UAAC;4DAAE,WAAU;;gEACV,oBAAA,+BAAA,gBAAA,QAAS,IAAI,cAAb,oCAAA,cAAe,SAAS;gEAAC;gEAAE,oBAAA,+BAAA,iBAAA,QAAS,IAAI,cAAb,qCAAA,eAAe,QAAQ;;;;;;;sEAErD,8UAAC;4DAAE,WAAU;sEACV,oBAAA,+BAAA,iBAAA,QAAS,IAAI,cAAb,sCAAA,qBAAA,eAAe,IAAI,cAAnB,yCAAA,mBAAqB,WAAW;;;;;;;;;;;;8DAGrC,8UAAC;oDAAI,WAAU;8DACb,cAAA,8UAAC,iLAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;wDACT,WAAU;;0EAEV,8UAAC,6UAAM;gEAAC,WAAU;;;;;;0EAClB,8UAAC;gEAAK,WAAU;0EAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS/C,8UAAC;wBAAK,WAAU;kCACd,cAAA,8UAAC;4BAAI,WAAU;;8CACb,8UAAC;oCAAI,WAAU;8CACb,cAAA,8UAAC;wCAAG,WAAU;kDAAuD;;;;;;;;;;;gCAEtE;;;;;;;;;;;;;;;;;;;;;;;;AAMb;GAzJwB;;QACI,6SAAU;QACrB,mSAAS;;;KAFF", "debugId": null}}, {"offset": {"line": 1154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/app/%28dash%29/teacher/page.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useSession } from 'next-auth/react'\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\r\nimport DashboardLayout from '@/components/layout/dashboard-layout'\r\nimport { \r\n  Users, \r\n  BookOpen, \r\n  Calendar,\r\n  BarChart3,\r\n  ClipboardList,\r\n  Award,\r\n  Clock,\r\n  TrendingUp,\r\n  FileText,\r\n  Bell\r\n} from 'lucide-react'\r\n\r\nconst teacherNavigation = [\r\n  { name: 'Dashboard', href: '/teacher', icon: BarChart3 },\r\n  { name: 'My Classes', href: '/teacher/classes', icon: BookOpen },\r\n  { name: 'Attendance', href: '/teacher/attendance', icon: ClipboardList },\r\n  { name: 'Marks', href: '/teacher/marks', icon: Award },\r\n  { name: 'Reports', href: '/teacher/reports', icon: FileText },\r\n  { name: 'Schedule', href: '/teacher/schedule', icon: Clock },\r\n]\r\n\r\nexport default function TeacherDashboard() {\r\n  const { data: session } = useSession()\r\n\r\n  // Mock data - in real app, this would come from API\r\n  const stats = {\r\n    totalClasses: 4,\r\n    totalStudents: 120,\r\n    averageAttendance: 95.2,\r\n    averageMarks: 82.5,\r\n    pendingTasks: 3,\r\n    upcomingExams: 2\r\n  }\r\n\r\n  const assignedClasses = [\r\n    {\r\n      name: 'Grade 8A',\r\n      subject: 'Mathematics',\r\n      students: 30,\r\n      attendance: 96.7,\r\n      nextClass: 'Today, 2:00 PM'\r\n    },\r\n    {\r\n      name: 'Grade 8B',\r\n      subject: 'Mathematics',\r\n      students: 28,\r\n      attendance: 94.1,\r\n      nextClass: 'Tomorrow, 9:00 AM'\r\n    },\r\n    {\r\n      name: 'Grade 9A',\r\n      subject: 'Physics',\r\n      students: 32,\r\n      attendance: 97.2,\r\n      nextClass: 'Today, 4:00 PM'\r\n    },\r\n    {\r\n      name: 'Grade 9B',\r\n      subject: 'Physics',\r\n      students: 30,\r\n      attendance: 93.8,\r\n      nextClass: 'Tomorrow, 11:00 AM'\r\n    }\r\n  ]\r\n\r\n  const quickActions = [\r\n    {\r\n      title: 'Take Attendance',\r\n      description: 'Mark today\\'s attendance',\r\n      icon: ClipboardList,\r\n      href: '/teacher/attendance',\r\n      color: 'bg-green-500'\r\n    },\r\n    {\r\n      title: 'Enter Marks',\r\n      description: 'Upload exam results',\r\n      icon: Award,\r\n      href: '/teacher/marks',\r\n      color: 'bg-blue-500'\r\n    },\r\n    {\r\n      title: 'Generate Report',\r\n      description: 'Create class reports',\r\n      icon: FileText,\r\n      href: '/teacher/reports',\r\n      color: 'bg-purple-500'\r\n    },\r\n    {\r\n      title: 'View Schedule',\r\n      description: 'Check your timetable',\r\n      icon: Clock,\r\n      href: '/teacher/schedule',\r\n      color: 'bg-orange-500'\r\n    }\r\n  ]\r\n\r\n  return (\r\n    <DashboardLayout title=\"Teacher Dashboard\" navigation={teacherNavigation}>\r\n      <div className=\"space-y-6\">\r\n        {/* Welcome Section */}\r\n        <div className=\"bg-white rounded-lg border p-6\">\r\n          <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">\r\n            Welcome back, {session?.user?.firstName || 'Teacher'}!\r\n          </h2>\r\n          <p className=\"text-gray-600\">\r\n            Here's your teaching overview and quick actions for today.\r\n          </p>\r\n        </div>\r\n\r\n        {/* Stats Cards */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n          <Card>\r\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n              <CardTitle className=\"text-sm font-medium\">Total Classes</CardTitle>\r\n              <BookOpen className=\"h-4 w-4 text-muted-foreground\" />\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"text-2xl font-bold\">{stats.totalClasses}</div>\r\n              <p className=\"text-xs text-muted-foreground\">\r\n                Assigned classes\r\n              </p>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          <Card>\r\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n              <CardTitle className=\"text-sm font-medium\">Total Students</CardTitle>\r\n              <Users className=\"h-4 w-4 text-muted-foreground\" />\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"text-2xl font-bold\">{stats.totalStudents}</div>\r\n              <p className=\"text-xs text-muted-foreground\">\r\n                Across all classes\r\n              </p>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          <Card>\r\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n              <CardTitle className=\"text-sm font-medium\">Average Attendance</CardTitle>\r\n              <TrendingUp className=\"h-4 w-4 text-muted-foreground\" />\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"text-2xl font-bold\">{stats.averageAttendance}%</div>\r\n              <p className=\"text-xs text-muted-foreground\">\r\n                This week\r\n              </p>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          <Card>\r\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n              <CardTitle className=\"text-sm font-medium\">Average Marks</CardTitle>\r\n              <Award className=\"h-4 w-4 text-muted-foreground\" />\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"text-2xl font-bold\">{stats.averageMarks}%</div>\r\n              <p className=\"text-xs text-muted-foreground\">\r\n                Current term\r\n              </p>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          <Card>\r\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n              <CardTitle className=\"text-sm font-medium\">Pending Tasks</CardTitle>\r\n              <Bell className=\"h-4 w-4 text-muted-foreground\" />\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"text-2xl font-bold\">{stats.pendingTasks}</div>\r\n              <p className=\"text-xs text-muted-foreground\">\r\n                Require attention\r\n              </p>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          <Card>\r\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n              <CardTitle className=\"text-sm font-medium\">Upcoming Exams</CardTitle>\r\n              <Calendar className=\"h-4 w-4 text-muted-foreground\" />\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"text-2xl font-bold\">{stats.upcomingExams}</div>\r\n              <p className=\"text-xs text-muted-foreground\">\r\n                This week\r\n              </p>\r\n            </CardContent>\r\n          </Card>\r\n        </div>\r\n\r\n        {/* Assigned Classes */}\r\n        <div className=\"bg-white rounded-lg border p-6\">\r\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">My Classes</h3>\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n            {assignedClasses.map((classInfo) => (\r\n              <Card key={classInfo.name} className=\"cursor-pointer hover:shadow-md transition-shadow\">\r\n                <CardHeader>\r\n                  <CardTitle className=\"text-lg\">{classInfo.name}</CardTitle>\r\n                  <CardDescription>{classInfo.subject}</CardDescription>\r\n                </CardHeader>\r\n                <CardContent>\r\n                  <div className=\"space-y-2\">\r\n                    <div className=\"flex justify-between text-sm\">\r\n                      <span className=\"text-gray-600\">Students:</span>\r\n                      <span className=\"font-medium\">{classInfo.students}</span>\r\n                    </div>\r\n                    <div className=\"flex justify-between text-sm\">\r\n                      <span className=\"text-gray-600\">Attendance:</span>\r\n                      <span className=\"font-medium text-green-600\">{classInfo.attendance}%</span>\r\n                    </div>\r\n                    <div className=\"flex justify-between text-sm\">\r\n                      <span className=\"text-gray-600\">Next Class:</span>\r\n                      <span className=\"font-medium\">{classInfo.nextClass}</span>\r\n                    </div>\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Quick Actions */}\r\n        <div className=\"bg-white rounded-lg border p-6\">\r\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Quick Actions</h3>\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\r\n            {quickActions.map((action) => (\r\n              <Card key={action.title} className=\"cursor-pointer hover:shadow-md transition-shadow\">\r\n                <CardContent className=\"p-4\">\r\n                  <div className=\"flex items-center space-x-3\">\r\n                    <div className={`p-2 rounded-lg ${action.color}`}>\r\n                      <action.icon className=\"h-5 w-5 text-white\" />\r\n                    </div>\r\n                    <div>\r\n                      <h4 className=\"font-medium text-sm\">{action.title}</h4>\r\n                      <p className=\"text-xs text-gray-500\">{action.description}</p>\r\n                    </div>\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Today's Schedule */}\r\n        <div className=\"bg-white rounded-lg border p-6\">\r\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Today's Schedule</h3>\r\n          <div className=\"space-y-3\">\r\n            <div className=\"flex items-center justify-between p-3 bg-blue-50 rounded-lg\">\r\n              <div className=\"flex items-center space-x-3\">\r\n                <div className=\"w-3 h-3 bg-blue-500 rounded-full\"></div>\r\n                <div>\r\n                  <p className=\"font-medium\">Grade 8A - Mathematics</p>\r\n                  <p className=\"text-sm text-gray-600\">Room 101</p>\r\n                </div>\r\n              </div>\r\n              <span className=\"text-sm font-medium\">9:00 AM - 10:00 AM</span>\r\n            </div>\r\n            <div className=\"flex items-center justify-between p-3 bg-green-50 rounded-lg\">\r\n              <div className=\"flex items-center space-x-3\">\r\n                <div className=\"w-3 h-3 bg-green-500 rounded-full\"></div>\r\n                <div>\r\n                  <p className=\"font-medium\">Grade 9A - Physics</p>\r\n                  <p className=\"text-sm text-gray-600\">Lab 2</p>\r\n                </div>\r\n              </div>\r\n              <span className=\"text-sm font-medium\">2:00 PM - 3:00 PM</span>\r\n            </div>\r\n            <div className=\"flex items-center justify-between p-3 bg-purple-50 rounded-lg\">\r\n              <div className=\"flex items-center space-x-3\">\r\n                <div className=\"w-3 h-3 bg-purple-500 rounded-full\"></div>\r\n                <div>\r\n                  <p className=\"font-medium\">Grade 8B - Mathematics</p>\r\n                  <p className=\"text-sm text-gray-600\">Room 102</p>\r\n                </div>\r\n              </div>\r\n              <span className=\"text-sm font-medium\">4:00 PM - 5:00 PM</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </DashboardLayout>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAkBA,MAAM,oBAAoB;IACxB;QAAE,MAAM;QAAa,MAAM;QAAY,MAAM,wVAAS;IAAC;IACvD;QAAE,MAAM;QAAc,MAAM;QAAoB,MAAM,mVAAQ;IAAC;IAC/D;QAAE,MAAM;QAAc,MAAM;QAAuB,MAAM,kWAAa;IAAC;IACvE;QAAE,MAAM;QAAS,MAAM;QAAkB,MAAM,sUAAK;IAAC;IACrD;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM,mVAAQ;IAAC;IAC5D;QAAE,MAAM;QAAY,MAAM;QAAqB,MAAM,sUAAK;IAAC;CAC5D;AAEc,SAAS;QAiFG;;IAhFzB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,6SAAU;IAEpC,oDAAoD;IACpD,MAAM,QAAQ;QACZ,cAAc;QACd,eAAe;QACf,mBAAmB;QACnB,cAAc;QACd,cAAc;QACd,eAAe;IACjB;IAEA,MAAM,kBAAkB;QACtB;YACE,MAAM;YACN,SAAS;YACT,UAAU;YACV,YAAY;YACZ,WAAW;QACb;QACA;YACE,MAAM;YACN,SAAS;YACT,UAAU;YACV,YAAY;YACZ,WAAW;QACb;QACA;YACE,MAAM;YACN,SAAS;YACT,UAAU;YACV,YAAY;YACZ,WAAW;QACb;QACA;YACE,MAAM;YACN,SAAS;YACT,UAAU;YACV,YAAY;YACZ,WAAW;QACb;KACD;IAED,MAAM,eAAe;QACnB;YACE,OAAO;YACP,aAAa;YACb,MAAM,kWAAa;YACnB,MAAM;YACN,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM,sUAAK;YACX,MAAM;YACN,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM,mVAAQ;YACd,MAAM;YACN,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM,sUAAK;YACX,MAAM;YACN,OAAO;QACT;KACD;IAED,qBACE,8UAAC,mMAAe;QAAC,OAAM;QAAoB,YAAY;kBACrD,cAAA,8UAAC;YAAI,WAAU;;8BAEb,8UAAC;oBAAI,WAAU;;sCACb,8UAAC;4BAAG,WAAU;;gCAA2C;gCACxC,CAAA,oBAAA,+BAAA,gBAAA,QAAS,IAAI,cAAb,oCAAA,cAAe,SAAS,KAAI;gCAAU;;;;;;;sCAEvD,8UAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAM/B,8UAAC;oBAAI,WAAU;;sCACb,8UAAC,6KAAI;;8CACH,8UAAC,mLAAU;oCAAC,WAAU;;sDACpB,8UAAC,kLAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,8UAAC,mVAAQ;4CAAC,WAAU;;;;;;;;;;;;8CAEtB,8UAAC,oLAAW;;sDACV,8UAAC;4CAAI,WAAU;sDAAsB,MAAM,YAAY;;;;;;sDACvD,8UAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,8UAAC,6KAAI;;8CACH,8UAAC,mLAAU;oCAAC,WAAU;;sDACpB,8UAAC,kLAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,8UAAC,sUAAK;4CAAC,WAAU;;;;;;;;;;;;8CAEnB,8UAAC,oLAAW;;sDACV,8UAAC;4CAAI,WAAU;sDAAsB,MAAM,aAAa;;;;;;sDACxD,8UAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,8UAAC,6KAAI;;8CACH,8UAAC,mLAAU;oCAAC,WAAU;;sDACpB,8UAAC,kLAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,8UAAC,yVAAU;4CAAC,WAAU;;;;;;;;;;;;8CAExB,8UAAC,oLAAW;;sDACV,8UAAC;4CAAI,WAAU;;gDAAsB,MAAM,iBAAiB;gDAAC;;;;;;;sDAC7D,8UAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,8UAAC,6KAAI;;8CACH,8UAAC,mLAAU;oCAAC,WAAU;;sDACpB,8UAAC,kLAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,8UAAC,sUAAK;4CAAC,WAAU;;;;;;;;;;;;8CAEnB,8UAAC,oLAAW;;sDACV,8UAAC;4CAAI,WAAU;;gDAAsB,MAAM,YAAY;gDAAC;;;;;;;sDACxD,8UAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,8UAAC,6KAAI;;8CACH,8UAAC,mLAAU;oCAAC,WAAU;;sDACpB,8UAAC,kLAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,8UAAC,mUAAI;4CAAC,WAAU;;;;;;;;;;;;8CAElB,8UAAC,oLAAW;;sDACV,8UAAC;4CAAI,WAAU;sDAAsB,MAAM,YAAY;;;;;;sDACvD,8UAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,8UAAC,6KAAI;;8CACH,8UAAC,mLAAU;oCAAC,WAAU;;sDACpB,8UAAC,kLAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,8UAAC,+UAAQ;4CAAC,WAAU;;;;;;;;;;;;8CAEtB,8UAAC,oLAAW;;sDACV,8UAAC;4CAAI,WAAU;sDAAsB,MAAM,aAAa;;;;;;sDACxD,8UAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;;8BAQnD,8UAAC;oBAAI,WAAU;;sCACb,8UAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,8UAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,0BACpB,8UAAC,6KAAI;oCAAsB,WAAU;;sDACnC,8UAAC,mLAAU;;8DACT,8UAAC,kLAAS;oDAAC,WAAU;8DAAW,UAAU,IAAI;;;;;;8DAC9C,8UAAC,wLAAe;8DAAE,UAAU,OAAO;;;;;;;;;;;;sDAErC,8UAAC,oLAAW;sDACV,cAAA,8UAAC;gDAAI,WAAU;;kEACb,8UAAC;wDAAI,WAAU;;0EACb,8UAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8UAAC;gEAAK,WAAU;0EAAe,UAAU,QAAQ;;;;;;;;;;;;kEAEnD,8UAAC;wDAAI,WAAU;;0EACb,8UAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8UAAC;gEAAK,WAAU;;oEAA8B,UAAU,UAAU;oEAAC;;;;;;;;;;;;;kEAErE,8UAAC;wDAAI,WAAU;;0EACb,8UAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8UAAC;gEAAK,WAAU;0EAAe,UAAU,SAAS;;;;;;;;;;;;;;;;;;;;;;;;mCAjB/C,UAAU,IAAI;;;;;;;;;;;;;;;;8BA2B/B,8UAAC;oBAAI,WAAU;;sCACb,8UAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,8UAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,uBACjB,8UAAC,6KAAI;oCAAoB,WAAU;8CACjC,cAAA,8UAAC,oLAAW;wCAAC,WAAU;kDACrB,cAAA,8UAAC;4CAAI,WAAU;;8DACb,8UAAC;oDAAI,WAAW,AAAC,kBAA8B,OAAb,OAAO,KAAK;8DAC5C,cAAA,8UAAC,OAAO,IAAI;wDAAC,WAAU;;;;;;;;;;;8DAEzB,8UAAC;;sEACC,8UAAC;4DAAG,WAAU;sEAAuB,OAAO,KAAK;;;;;;sEACjD,8UAAC;4DAAE,WAAU;sEAAyB,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;;mCARrD,OAAO,KAAK;;;;;;;;;;;;;;;;8BAkB7B,8UAAC;oBAAI,WAAU;;sCACb,8UAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,8UAAC;4BAAI,WAAU;;8CACb,8UAAC;oCAAI,WAAU;;sDACb,8UAAC;4CAAI,WAAU;;8DACb,8UAAC;oDAAI,WAAU;;;;;;8DACf,8UAAC;;sEACC,8UAAC;4DAAE,WAAU;sEAAc;;;;;;sEAC3B,8UAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAGzC,8UAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;;8CAExC,8UAAC;oCAAI,WAAU;;sDACb,8UAAC;4CAAI,WAAU;;8DACb,8UAAC;oDAAI,WAAU;;;;;;8DACf,8UAAC;;sEACC,8UAAC;4DAAE,WAAU;sEAAc;;;;;;sEAC3B,8UAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAGzC,8UAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;;8CAExC,8UAAC;oCAAI,WAAU;;sDACb,8UAAC;4CAAI,WAAU;;8DACb,8UAAC;oDAAI,WAAU;;;;;;8DACf,8UAAC;;sEACC,8UAAC;4DAAE,WAAU;sEAAc;;;;;;sEAC3B,8UAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAGzC,8UAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpD;GArQwB;;QACI,6SAAU;;;KADd", "debugId": null}}]}