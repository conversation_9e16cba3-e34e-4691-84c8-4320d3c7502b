import { NextResponse } from 'next/server'

// Temporarily disabled authentication middleware for testing
export default function middleware(req) {
  // Allow all requests to pass through without authentication
  return NextResponse.next()
}

export const config = {
  matcher: [
    '/admin/:path*',
    '/teacher/:path*',
    '/student/:path*',
    '/api/admin/:path*',
    '/api/teacher/:path*',
    '/api/student/:path*'
  ]
}
