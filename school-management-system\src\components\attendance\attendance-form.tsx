'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';

interface Student {
  id: number;
  firstName: string;
  lastName: string;
  rollNumber: string;
}

interface Class {
  id: number;
  name: string;
  section: {
    name: string;
  };
  students: Student[];
}

interface AttendanceFormProps {
  classData?: Class;
  date?: string;
}

export default function AttendanceForm({ classData, date = new Date().toISOString().split('T')[0] }: AttendanceFormProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [selectedDate, setSelectedDate] = useState(date);
  const [selectedClass, setSelectedClass] = useState<Class | null>(classData || null);
  const [classes, setClasses] = useState<Class[]>([]);
  const [attendanceRecords, setAttendanceRecords] = useState<{[key: number]: string}>({});

  useEffect(() => {
    // Fetch teacher's classes
    const fetchClasses = async () => {
      try {
        const response = await fetch('/api/admin/classes');
        if (response.ok) {
          const data = await response.json();
          setClasses(data.classes);
        }
      } catch (error) {
        console.error('Error fetching classes:', error);
      }
    };

    fetchClasses();
  }, []);

  useEffect(() => {
    if (selectedClass) {
      // Initialize attendance records for all students
      const initialRecords: {[key: number]: string} = {};
      selectedClass.students.forEach(student => {
        initialRecords[student.id] = 'PRESENT';
      });
      setAttendanceRecords(initialRecords);
    }
  }, [selectedClass]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedClass) {
      setError('Please select a class');
      return;
    }

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const attendanceData = Object.entries(attendanceRecords).map(([studentId, status]) => ({
        studentId: parseInt(studentId),
        status: status as 'PRESENT' | 'ABSENT' | 'LATE' | 'HALF_DAY',
      }));

      const payload = {
        classId: selectedClass.id,
        date: selectedDate,
        attendanceRecords: attendanceData,
      };

      const response = await fetch('/api/teacher/attendance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to mark attendance');
      }

      setSuccess(data.message);
      
      // Reset form after successful submission
      setTimeout(() => {
        router.push('/teacher/attendance');
      }, 2000);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleAttendanceChange = (studentId: number, status: string) => {
    setAttendanceRecords(prev => ({
      ...prev,
      [studentId]: status,
    }));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PRESENT': return 'bg-green-100 text-green-800';
      case 'ABSENT': return 'bg-red-100 text-red-800';
      case 'LATE': return 'bg-yellow-100 text-yellow-800';
      case 'HALF_DAY': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>Mark Attendance</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert>
              <AlertDescription>{success}</AlertDescription>
            </Alert>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="class">Class *</Label>
              <select
                id="class"
                value={selectedClass?.id || ''}
                onChange={(e) => {
                  const classId = parseInt(e.target.value);
                  const classData = classes.find(c => c.id === classId);
                  setSelectedClass(classData || null);
                }}
                required
                disabled={loading}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="">Select Class</option>
                {classes.map((cls) => (
                  <option key={cls.id} value={cls.id}>
                    {cls.name} {cls.section.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <Label htmlFor="date">Date *</Label>
              <Input
                id="date"
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                required
                disabled={loading}
              />
            </div>
          </div>

          {selectedClass && (
            <div>
              <h3 className="text-lg font-semibold mb-4">
                Students in {selectedClass.name} {selectedClass.section.name}
              </h3>
              
              <div className="space-y-3">
                {selectedClass.students.map((student) => (
                  <div key={student.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div className="font-medium">
                        {student.firstName} {student.lastName}
                      </div>
                      <div className="text-sm text-gray-500">
                        Roll No: {student.rollNumber}
                      </div>
                    </div>
                    
                    <div className="flex gap-2">
                      {['PRESENT', 'ABSENT', 'LATE', 'HALF_DAY'].map((status) => (
                        <button
                          key={status}
                          type="button"
                          onClick={() => handleAttendanceChange(student.id, status)}
                          className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                            attendanceRecords[student.id] === status
                              ? getStatusColor(status)
                              : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                          }`}
                        >
                          {status}
                        </button>
                      ))}
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-6 flex justify-between items-center">
                <div className="text-sm text-gray-600">
                  Total Students: {selectedClass.students.length}
                </div>
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      // Mark all as present
                      const allPresent: {[key: number]: string} = {};
                      selectedClass.students.forEach(student => {
                        allPresent[student.id] = 'PRESENT';
                      });
                      setAttendanceRecords(allPresent);
                    }}
                    disabled={loading}
                  >
                    Mark All Present
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      // Mark all as absent
                      const allAbsent: {[key: number]: string} = {};
                      selectedClass.students.forEach(student => {
                        allAbsent[student.id] = 'ABSENT';
                      });
                      setAttendanceRecords(allAbsent);
                    }}
                    disabled={loading}
                  >
                    Mark All Absent
                  </Button>
                </div>
              </div>
            </div>
          )}

          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading || !selectedClass}>
              {loading ? 'Saving...' : 'Mark Attendance'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
