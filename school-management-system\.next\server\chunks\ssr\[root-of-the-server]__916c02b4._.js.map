{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sQAAO,EAAC,IAAA,gOAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 19, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h3\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <p\r\n    ref={ref}\r\n    className={cn(\"text-sm text-gray-600 dark:text-gray-400\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,oWAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,+HACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,oWAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,oWAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAA<PERSON>;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,oWAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EAAC,4CAA4C;QACzD,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,oWAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QAAI,KAAK;QAAK,WAAW,IAAA,2JAAE,EAAC,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,oWAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700\",\r\n        destructive:\r\n          \"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700\",\r\n        outline:\r\n          \"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100\",\r\n        secondary:\r\n          \"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700\",\r\n        ghost: \"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100\",\r\n        link: \"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,IAAA,oRAAG,EACxB,uQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,oWAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,+UAAI,GAAG;IAC9B,qBACE,+XAAC;QACC,WAAW,IAAA,2JAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nexport interface InputProps\r\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-10 w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 px-3 py-2 text-sm text-gray-900 dark:text-gray-100 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 dark:placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nInput.displayName = \"Input\"\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,oWAAgB,CAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,+XAAC;QACC,MAAM;QACN,WAAW,IAAA,2JAAE,EACX,yaACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 192, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n)\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\r\n    VariantProps<typeof labelVariants>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(labelVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,gBAAgB,IAAA,oRAAG,EACvB;AAGF,MAAM,sBAAQ,oWAAgB,CAI5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC,wTAAmB;QAClB,KAAK;QACL,WAAW,IAAA,2JAAE,EAAC,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,wTAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 240, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["'use client'\n\nimport * as React from 'react'\nimport * as DropdownMenuPrimitive from '@radix-ui/react-dropdown-menu'\nimport { Check, ChevronRight, Circle } from 'lucide-react'\nimport { cn } from '@/lib/utils'\n\nconst DropdownMenu = DropdownMenuPrimitive.Root\n\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\n\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\n\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\n\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\n\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      'flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\n  </DropdownMenuPrimitive.SubTrigger>\n))\nDropdownMenuSubTrigger.displayName = DropdownMenuPrimitive.SubTrigger.displayName\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      'z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuSubContent.displayName = DropdownMenuPrimitive.SubContent.displayName\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        'z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',\n        className\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n))\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      'relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      'relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.CheckboxItem>\n))\nDropdownMenuCheckboxItem.displayName = DropdownMenuPrimitive.CheckboxItem.displayName\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      'relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.RadioItem>\n))\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      'px-2 py-1.5 text-sm font-semibold',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn('-mx-1 my-1 h-px bg-muted', className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\n\nconst DropdownMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn('ml-auto text-xs tracking-widest opacity-60', className)}\n      {...props}\n    />\n  )\n}\nDropdownMenuShortcut.displayName = 'DropdownMenuShortcut'\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AALA;;;;;;AAOA,MAAM,eAAe,6TAA0B;AAE/C,MAAM,sBAAsB,gUAA6B;AAEzD,MAAM,oBAAoB,8TAA2B;AAErD,MAAM,qBAAqB,+TAA4B;AAEvD,MAAM,kBAAkB,4TAAyB;AAEjD,MAAM,yBAAyB,mUAAgC;AAE/D,MAAM,uCAAyB,oWAAgB,CAK7C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,+XAAC,mUAAgC;QAC/B,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,wIACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,+XAAC,4VAAY;gBAAC,WAAU;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAAG,mUAAgC,CAAC,WAAW;AAEjF,MAAM,uCAAyB,oWAAgB,CAG7C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC,mUAAgC;QAC/B,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,ybACA;QAED,GAAG,KAAK;;;;;;AAGb,uBAAuB,WAAW,GAAG,mUAAgC,CAAC,WAAW;AAEjF,MAAM,oCAAsB,oWAAgB,CAG1C,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,+XAAC,+TAA4B;kBAC3B,cAAA,+XAAC,gUAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,IAAA,2JAAE,EACX,ybACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,gUAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,oWAAgB,CAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,+XAAC,6TAA0B;QACzB,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,mOACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,6TAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,oWAAgB,CAG/C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,+XAAC,qUAAkC;QACjC,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,+XAAC;gBAAK,WAAU;0BACd,cAAA,+XAAC,sUAAmC;8BAClC,cAAA,+XAAC,mUAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;AAGL,yBAAyB,WAAW,GAAG,qUAAkC,CAAC,WAAW;AAErF,MAAM,sCAAwB,oWAAgB,CAG5C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,+XAAC,kUAA+B;QAC9B,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,wOACA;QAED,GAAG,KAAK;;0BAET,+XAAC;gBAAK,WAAU;0BACd,cAAA,+XAAC,sUAAmC;8BAClC,cAAA,+XAAC,sUAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;AAGL,sBAAsB,WAAW,GAAG,kUAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,oWAAgB,CAKxC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,+XAAC,8TAA2B;QAC1B,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,8TAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,oWAAgB,CAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC,kUAA+B;QAC9B,KAAK;QACL,WAAW,IAAA,2JAAE,EAAC,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,sBAAsB,WAAW,GAAG,kUAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,+XAAC;QACC,WAAW,IAAA,2JAAE,EAAC,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;AACA,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 451, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/theme-toggle.tsx"], "sourcesContent": ["'use client'\n\nimport { <PERSON>, Sun, Monitor } from 'lucide-react'\nimport { Button } from './button'\nimport { useTheme } from '@/components/providers/theme-provider'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from './dropdown-menu'\n\nexport function ThemeToggle() {\n  const { theme, actualTheme, mounted, setTheme } = useTheme()\n\n  const toggleTheme = () => {\n    if (!mounted) return\n    const next = actualTheme === 'light' ? 'dark' : 'light'\n    setTheme(next)\n  }\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button\n          variant=\"ghost\"\n          size=\"icon\"\n          className=\"h-9 w-9\"\n          onClick={toggleTheme}\n          title={`Switch to ${actualTheme === 'light' ? 'dark' : 'light'} mode`}\n        >\n          <Sun className=\"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n          <Moon className=\"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n          <span className=\"sr-only\">Toggle theme</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\">\n        <DropdownMenuItem onClick={() => setTheme('light')}>\n          <Sun className=\"mr-2 h-4 w-4\" />\n          <span>Light</span>\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme('dark')}>\n          <Moon className=\"mr-2 h-4 w-4\" />\n          <span>Dark</span>\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme('system')}>\n          <Monitor className=\"mr-2 h-4 w-4\" />\n          <span>System</span>\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AAAA;AACA;AACA;AACA;AALA;;;;;;AAYO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,IAAA,kMAAQ;IAE1D,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;QACd,MAAM,OAAO,gBAAgB,UAAU,SAAS;QAChD,SAAS;IACX;IAEA,qBACE,+XAAC,8LAAY;;0BACX,+XAAC,qMAAmB;gBAAC,OAAO;0BAC1B,cAAA,+XAAC,8KAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,SAAS;oBACT,OAAO,CAAC,UAAU,EAAE,gBAAgB,UAAU,SAAS,QAAQ,KAAK,CAAC;;sCAErE,+XAAC,6TAAG;4BAAC,WAAU;;;;;;sCACf,+XAAC,gUAAI;4BAAC,WAAU;;;;;;sCAChB,+XAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,+XAAC,qMAAmB;gBAAC,OAAM;;kCACzB,+XAAC,kMAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,+XAAC,6TAAG;gCAAC,WAAU;;;;;;0CACf,+XAAC;0CAAK;;;;;;;;;;;;kCAER,+XAAC,kMAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,+XAAC,gUAAI;gCAAC,WAAU;;;;;;0CAChB,+XAAC;0CAAK;;;;;;;;;;;;kCAER,+XAAC,kMAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,+XAAC,yUAAO;gCAAC,WAAU;;;;;;0CACnB,+XAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAKhB", "debugId": null}}, {"offset": {"line": 608, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/layout/dashboard-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useSession, signOut } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { ThemeToggle } from '@/components/ui/theme-toggle'\nimport {\n  Menu,\n  X,\n  User,\n  LogOut,\n  Settings,\n  Bell,\n  Search,\n  School,\n  Plus,\n  Upload,\n  Download,\n  Users,\n  BookOpen,\n  GraduationCap,\n  FileText,\n  BarChart3,\n  Calendar,\n  Home,\n  Edit,\n  ClipboardList,\n  Award\n} from 'lucide-react'\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n  title: string\n  navigation: {\n    name: string\n    href: string\n    icon: string\n  }[]\n}\n\n// Icon mapping object\nconst iconMap: Record<string, React.ComponentType<{ className?: string }>> = {\n  Plus,\n  Upload,\n  Download,\n  Users,\n  BookOpen,\n  GraduationCap,\n  FileText,\n  BarChart3,\n  Calendar,\n  Home,\n  Settings,\n  Bell,\n  User,\n  Edit,\n  ClipboardList,\n  Award\n}\n\nexport default function DashboardLayout({ children, title, navigation }: DashboardLayoutProps) {\n  const { data: session } = useSession()\n  const router = useRouter()\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n\n  const handleSignOut = async () => {\n    await signOut({ callbackUrl: '/' })\n  }\n\n  const getIcon = (iconName: string) => {\n    const IconComponent = iconMap[iconName]\n    return IconComponent || Home // fallback to Home icon if not found\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-950\">\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"fixed inset-y-0 left-0 flex w-64 flex-col bg-white dark:bg-gray-900\">\n          <div className=\"flex h-16 items-center justify-between px-4\">\n            <div className=\"flex items-center\">\n              <School className=\"h-8 w-8 text-blue-600\" />\n              <span className=\"ml-2 text-lg font-semibold\">SMS</span>\n            </div>\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <X className=\"h-5 w-5\" />\n            </Button>\n          </div>\n          <nav className=\"flex-1 space-y-1 px-2 py-4\">\n            {navigation.map((item) => {\n              const IconComponent = getIcon(item.icon)\n              return (\n                <Button\n                  key={item.name}\n                  variant=\"ghost\"\n                  className=\"w-full justify-start\"\n                  onClick={() => {\n                    router.push(item.href)\n                    setSidebarOpen(false)\n                  }}\n                >\n                  <IconComponent className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </Button>\n              )\n            })}\n          </nav>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex flex-col flex-grow bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800\">\n          <div className=\"flex h-16 items-center px-4\">\n            <School className=\"h-8 w-8 text-blue-600\" />\n            <span className=\"ml-2 text-lg font-semibold hidden xl:inline\">School Management System</span>\n            <span className=\"ml-2 text-lg font-semibold xl:hidden\">SMS</span>\n          </div>\n          <nav className=\"flex-1 space-y-1 px-2 py-4\">\n            {navigation.map((item) => {\n              const IconComponent = getIcon(item.icon)\n              return (\n                <Button\n                  key={item.name}\n                  variant=\"ghost\"\n                  className=\"w-full justify-start\"\n                  onClick={() => router.push(item.href)}\n                >\n                  <IconComponent className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </Button>\n              )\n            })}\n          </nav>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Top bar */}\n        <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"lg:hidden\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <Menu className=\"h-5 w-5\" />\n          </Button>\n\n          <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\n            <div className=\"relative flex flex-1\">\n              <div className=\"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3\">\n                <Search className=\"h-5 w-5 text-gray-400\" />\n              </div>\n              <input\n                type=\"text\"\n                placeholder=\"Search...\"\n                className=\"block h-full w-full border-0 py-0 pl-10 pr-0 text-gray-900 dark:text-gray-100 placeholder:text-gray-400 dark:placeholder:text-gray-500 focus:ring-0 sm:text-sm bg-transparent\"\n              />\n            </div>\n          </div>\n\n          <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\n            <ThemeToggle />\n\n            <Button variant=\"ghost\" size=\"sm\">\n              <Bell className=\"h-5 w-5\" />\n            </Button>\n\n            <div className=\"relative\">\n              <div className=\"flex items-center gap-x-3\">\n                <div className=\"text-sm hidden sm:block\">\n                  <p className=\"font-medium text-gray-900 dark:text-gray-100\">\n                    {session?.user?.firstName} {session?.user?.lastName}\n                  </p>\n                  <p className=\"text-gray-500 dark:text-gray-400 capitalize\">\n                    {session?.user?.role?.toLowerCase()}\n                  </p>\n                </div>\n                <div className=\"flex items-center gap-x-2\">\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={handleSignOut}\n                    className=\"flex items-center gap-2\"\n                  >\n                    <LogOut className=\"h-4 w-4\" />\n                    <span className=\"hidden sm:inline\">Sign Out</span>\n                  </Button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"py-6\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            <div className=\"mb-6\">\n              <h1 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">{title}</h1>\n            </div>\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;AA0CA,sBAAsB;AACtB,MAAM,UAAuE;IAC3E,MAAA,gUAAI;IACJ,QAAA,sUAAM;IACN,UAAA,4UAAQ;IACR,OAAA,mUAAK;IACL,UAAA,gVAAQ;IACR,eAAA,+VAAa;IACb,UAAA,gVAAQ;IACR,WAAA,qVAAS;IACT,UAAA,4UAAQ;IACR,MAAA,iUAAI;IACJ,UAAA,4UAAQ;IACR,MAAA,gUAAI;IACJ,MAAA,gUAAI;IACJ,MAAA,yUAAI;IACJ,eAAA,+VAAa;IACb,OAAA,mUAAK;AACP;AAEe,SAAS,gBAAgB,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAwB;IAC3F,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,0SAAU;IACpC,MAAM,SAAS,IAAA,gSAAS;IACxB,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,kWAAQ,EAAC;IAE/C,MAAM,gBAAgB;QACpB,MAAM,IAAA,uSAAO,EAAC;YAAE,aAAa;QAAI;IACnC;IAEA,MAAM,UAAU,CAAC;QACf,MAAM,gBAAgB,OAAO,CAAC,SAAS;QACvC,OAAO,iBAAiB,iUAAI,CAAC,qCAAqC;;IACpE;IAEA,qBACE,+XAAC;QAAI,WAAU;;0BAEb,+XAAC;gBAAI,WAAW,CAAC,6BAA6B,EAAE,cAAc,UAAU,UAAU;;kCAChF,+XAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,+XAAC;wBAAI,WAAU;;0CACb,+XAAC;gCAAI,WAAU;;kDACb,+XAAC;wCAAI,WAAU;;0DACb,+XAAC,sUAAM;gDAAC,WAAU;;;;;;0DAClB,+XAAC;gDAAK,WAAU;0DAA6B;;;;;;;;;;;;kDAE/C,+XAAC,8KAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe;kDAE9B,cAAA,+XAAC,uTAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGjB,+XAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC;oCACf,MAAM,gBAAgB,QAAQ,KAAK,IAAI;oCACvC,qBACE,+XAAC,8KAAM;wCAEL,SAAQ;wCACR,WAAU;wCACV,SAAS;4CACP,OAAO,IAAI,CAAC,KAAK,IAAI;4CACrB,eAAe;wCACjB;;0DAEA,+XAAC;gDAAc,WAAU;;;;;;4CACxB,KAAK,IAAI;;uCATL,KAAK,IAAI;;;;;gCAYpB;;;;;;;;;;;;;;;;;;0BAMN,+XAAC;gBAAI,WAAU;0BACb,cAAA,+XAAC;oBAAI,WAAU;;sCACb,+XAAC;4BAAI,WAAU;;8CACb,+XAAC,sUAAM;oCAAC,WAAU;;;;;;8CAClB,+XAAC;oCAAK,WAAU;8CAA8C;;;;;;8CAC9D,+XAAC;oCAAK,WAAU;8CAAuC;;;;;;;;;;;;sCAEzD,+XAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,gBAAgB,QAAQ,KAAK,IAAI;gCACvC,qBACE,+XAAC,8KAAM;oCAEL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,OAAO,IAAI,CAAC,KAAK,IAAI;;sDAEpC,+XAAC;4CAAc,WAAU;;;;;;wCACxB,KAAK,IAAI;;mCANL,KAAK,IAAI;;;;;4BASpB;;;;;;;;;;;;;;;;;0BAMN,+XAAC;gBAAI,WAAU;;kCAEb,+XAAC;wBAAI,WAAU;;0CACb,+XAAC,8KAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,eAAe;0CAE9B,cAAA,+XAAC,gUAAI;oCAAC,WAAU;;;;;;;;;;;0CAGlB,+XAAC;gCAAI,WAAU;0CACb,cAAA,+XAAC;oCAAI,WAAU;;sDACb,+XAAC;4CAAI,WAAU;sDACb,cAAA,+XAAC,sUAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,+XAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;;;;;;;;;;;;0CAKhB,+XAAC;gCAAI,WAAU;;kDACb,+XAAC,4LAAW;;;;;kDAEZ,+XAAC,8KAAM;wCAAC,SAAQ;wCAAQ,MAAK;kDAC3B,cAAA,+XAAC,gUAAI;4CAAC,WAAU;;;;;;;;;;;kDAGlB,+XAAC;wCAAI,WAAU;kDACb,cAAA,+XAAC;4CAAI,WAAU;;8DACb,+XAAC;oDAAI,WAAU;;sEACb,+XAAC;4DAAE,WAAU;;gEACV,SAAS,MAAM;gEAAU;gEAAE,SAAS,MAAM;;;;;;;sEAE7C,+XAAC;4DAAE,WAAU;sEACV,SAAS,MAAM,MAAM;;;;;;;;;;;;8DAG1B,+XAAC;oDAAI,WAAU;8DACb,cAAA,+XAAC,8KAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;wDACT,WAAU;;0EAEV,+XAAC,0UAAM;gEAAC,WAAU;;;;;;0EAClB,+XAAC;gEAAK,WAAU;0EAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS/C,+XAAC;wBAAK,WAAU;kCACd,cAAA,+XAAC;4BAAI,WAAU;;8CACb,+XAAC;oCAAI,WAAU;8CACb,cAAA,+XAAC;wCAAG,WAAU;kDAAuD;;;;;;;;;;;gCAEtE;;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 1092, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/navigation.ts"], "sourcesContent": ["// Shared navigation configurations for different user roles\n\nexport const adminNavigation = [\n  { name: 'Dashboard', href: '/admin', icon: 'BarChart3' },\n  { name: 'Students', href: '/admin/students', icon: 'Users' },\n  { name: 'Teachers', href: '/admin/teachers', icon: 'GraduationCap' },\n  { name: 'Classes & Sections', href: '/admin/classes', icon: 'BookOpen' },\n  { name: 'Subjects', href: '/admin/subjects', icon: 'FileText' },\n  { name: 'Terms & Exams', href: '/admin/exams', icon: 'Calendar' },\n  { name: 'Attendance', href: '/admin/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/admin/marks', icon: 'Award' },\n  { name: 'Reports', href: '/admin/reports', icon: 'FileText' },\n  { name: 'Settings', href: '/admin/settings', icon: 'Settings' },\n];\n\nexport const teacherNavigation = [\n  { name: 'Dashboard', href: '/teacher', icon: 'BarChart3' },\n  { name: 'My Classes', href: '/teacher/classes', icon: 'BookOpen' },\n  { name: 'Attendance', href: '/teacher/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/teacher/marks', icon: 'Award' },\n  { name: 'Students', href: '/teacher/students', icon: 'Users' },\n  { name: 'Reports', href: '/teacher/reports', icon: 'FileText' },\n  { name: 'Profile', href: '/teacher/profile', icon: 'User' },\n];\n\nexport const studentNavigation = [\n  { name: 'Dashboard', href: '/student', icon: 'BarChart3' },\n  { name: 'My Classes', href: '/student/classes', icon: 'BookOpen' },\n  { name: 'Attendance', href: '/student/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/student/marks', icon: 'Award' },\n  { name: 'Reports', href: '/student/reports', icon: 'FileText' },\n  { name: 'Profile', href: '/student/profile', icon: 'User' },\n];"], "names": [], "mappings": "AAAA,4DAA4D;;;;;;;;;AAErD,MAAM,kBAAkB;IAC7B;QAAE,MAAM;QAAa,MAAM;QAAU,MAAM;IAAY;IACvD;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAQ;IAC3D;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAgB;IACnE;QAAE,MAAM;QAAsB,MAAM;QAAkB,MAAM;IAAW;IACvE;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAiB,MAAM;QAAgB,MAAM;IAAW;IAChE;QAAE,MAAM;QAAc,MAAM;QAAqB,MAAM;IAAgB;IACvE;QAAE,MAAM;QAAS,MAAM;QAAgB,MAAM;IAAQ;IACrD;QAAE,MAAM;QAAW,MAAM;QAAkB,MAAM;IAAW;IAC5D;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAW;CAC/D;AAEM,MAAM,oBAAoB;IAC/B;QAAE,MAAM;QAAa,MAAM;QAAY,MAAM;IAAY;IACzD;QAAE,MAAM;QAAc,MAAM;QAAoB,MAAM;IAAW;IACjE;QAAE,MAAM;QAAc,MAAM;QAAuB,MAAM;IAAgB;IACzE;QAAE,MAAM;QAAS,MAAM;QAAkB,MAAM;IAAQ;IACvD;QAAE,MAAM;QAAY,MAAM;QAAqB,MAAM;IAAQ;IAC7D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAO;CAC3D;AAEM,MAAM,oBAAoB;IAC/B;QAAE,MAAM;QAAa,MAAM;QAAY,MAAM;IAAY;IACzD;QAAE,MAAM;QAAc,MAAM;QAAoB,MAAM;IAAW;IACjE;QAAE,MAAM;QAAc,MAAM;QAAuB,MAAM;IAAgB;IACzE;QAAE,MAAM;QAAS,MAAM;QAAkB,MAAM;IAAQ;IACvD;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAO;CAC3D", "debugId": null}}, {"offset": {"line": 1226, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/app/%28dash%29/admin/settings/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport DashboardLayout from '@/components/layout/dashboard-layout'\nimport { \n  Calendar, \n  Settings, \n  School, \n  Users, \n  Bell,\n  Shield,\n  Database,\n  Mail,\n  Save,\n  RefreshCw,\n  CheckCircle,\n  AlertCircle\n} from 'lucide-react'\n\ninterface SchoolInfo {\n  name: string\n  address: string\n  phone: string\n  email: string\n  website: string\n  principal: string\n  establishedYear: string\n}\n\ninterface AcademicSettings {\n  academicYear: string\n  currentTerm: string\n  gradingSystem: 'LETTER' | 'PERCENTAGE' | 'NUMERIC'\n  passPercentage: number\n  maxAttendancePercentage: number\n}\n\ninterface NotificationSettings {\n  attendanceAlerts: boolean\n  examResults: boolean\n  reportCardGeneration: boolean\n  systemUpdates: boolean\n}\n\ninterface SecuritySettings {\n  sessionTimeout: number\n  passwordPolicy: string\n  twoFactorAuth: boolean\n  loginAttempts: boolean\n}\n\nimport { adminNavigation } from '@/lib/navigation';\n\nexport default function SettingsPage() {\n  const [activeTab, setActiveTab] = useState<'general' | 'academic' | 'notifications' | 'security'>('general')\n  const [loading, setLoading] = useState(false)\n  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)\n  \n  const [schoolInfo, setSchoolInfo] = useState<SchoolInfo>({\n    name: 'Advance School',\n    address: '123 Education Street, City, State 12345',\n    phone: '+****************',\n    email: '<EMAIL>',\n    website: 'www.advanceschool.edu',\n    principal: 'Dr. John Smith',\n    establishedYear: '1995'\n  })\n  const [academicSettings, setAcademicSettings] = useState<AcademicSettings>({\n    academicYear: '2024-2025',\n    currentTerm: 'Term 1',\n    gradingSystem: 'LETTER',\n    passPercentage: 40,\n    maxAttendancePercentage: 75\n  })\n  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>({\n    attendanceAlerts: true,\n    examResults: true,\n    reportCardGeneration: false,\n    systemUpdates: true\n  })\n  const [securitySettings, setSecuritySettings] = useState<SecuritySettings>({\n    sessionTimeout: 30,\n    passwordPolicy: 'strong',\n    twoFactorAuth: false,\n    loginAttempts: true\n  })\n\n  // Load settings from API\n  useEffect(() => {\n    const loadSettings = async () => {\n      try {\n        const response = await fetch('/api/admin/settings')\n        if (response.ok) {\n          const data = await response.json()\n          if (data.general) {\n            setSchoolInfo({\n              name: data.general.schoolName,\n              address: data.general.address,\n              phone: data.general.phone,\n              email: data.general.email,\n              website: data.general.website,\n              principal: data.general.principal,\n              establishedYear: data.general.establishedYear\n            })\n          }\n          if (data.academic) {\n            setAcademicSettings({\n              academicYear: data.academic.academicYear,\n              currentTerm: data.academic.currentTerm,\n              gradingSystem: data.academic.gradingSystem,\n              passPercentage: data.academic.passPercentage,\n              maxAttendancePercentage: data.academic.maxAttendancePercentage\n            })\n          }\n          if (data.notifications) {\n            setNotificationSettings({\n              attendanceAlerts: data.notifications.attendanceAlerts,\n              examResults: data.notifications.examResults,\n              reportCardGeneration: data.notifications.reportCardGeneration,\n              systemUpdates: data.notifications.systemUpdates\n            })\n          }\n          if (data.security) {\n            setSecuritySettings({\n              sessionTimeout: data.security.sessionTimeout,\n              passwordPolicy: data.security.passwordPolicy,\n              twoFactorAuth: data.security.twoFactorAuth,\n              loginAttempts: data.security.loginAttempts\n            })\n          }\n        }\n      } catch (error) {\n        console.error('Error loading settings:', error)\n      }\n    }\n\n    loadSettings()\n  }, [])\n\n  const showMessage = (type: 'success' | 'error', text: string) => {\n    setMessage({ type, text })\n    setTimeout(() => setMessage(null), 3000)\n  }\n\n  const handleSaveSchoolInfo = async () => {\n    setLoading(true)\n    try {\n      const response = await fetch('/api/admin/settings', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ type: 'general', data: schoolInfo })\n      })\n      \n      if (response.ok) {\n        showMessage('success', 'School information saved successfully!')\n      } else {\n        showMessage('error', 'Failed to save school information')\n      }\n    } catch (error) {\n      showMessage('error', 'Error saving school information')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleSaveAcademicSettings = async () => {\n    setLoading(true)\n    try {\n      const response = await fetch('/api/admin/settings', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ type: 'academic', data: academicSettings })\n      })\n      \n      if (response.ok) {\n        showMessage('success', 'Academic settings saved successfully!')\n      } else {\n        showMessage('error', 'Failed to save academic settings')\n      }\n    } catch (error) {\n      showMessage('error', 'Error saving academic settings')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleSaveNotificationSettings = async () => {\n    setLoading(true)\n    try {\n      const response = await fetch('/api/admin/settings', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ type: 'notifications', data: notificationSettings })\n      })\n      \n      if (response.ok) {\n        showMessage('success', 'Notification settings saved successfully!')\n      } else {\n        showMessage('error', 'Failed to save notification settings')\n      }\n    } catch (error) {\n      showMessage('error', 'Error saving notification settings')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleSaveSecuritySettings = async () => {\n    setLoading(true)\n    try {\n      const response = await fetch('/api/admin/settings', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ type: 'security', data: securitySettings })\n      })\n      \n      if (response.ok) {\n        showMessage('success', 'Security settings saved successfully!')\n      } else {\n        showMessage('error', 'Failed to save security settings')\n      }\n    } catch (error) {\n      showMessage('error', 'Error saving security settings')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <DashboardLayout title=\"System Settings\" navigation={adminNavigation}>\n      <div className=\"space-y-6\">\n        {/* Message Display */}\n        {message && (\n          <div className={`p-4 rounded-md flex items-center ${\n            message.type === 'success' \n              ? 'bg-green-50 border border-green-200 text-green-800' \n              : 'bg-red-50 border border-red-200 text-red-800'\n          }`}>\n            {message.type === 'success' ? (\n              <CheckCircle className=\"w-5 h-5 mr-2\" />\n            ) : (\n              <AlertCircle className=\"w-5 h-5 mr-2\" />\n            )}\n            {message.text}\n          </div>\n        )}\n\n        {/* Header */}\n        <div className=\"flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-center sm:space-y-0\">\n          <div>\n            <h1 className=\"text-xl sm:text-2xl font-bold text-gray-900\">System Settings</h1>\n            <p className=\"text-sm sm:text-base text-gray-600\">Manage school configuration and preferences</p>\n          </div>\n          <div className=\"flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2\">\n            <Button variant=\"outline\" disabled={loading} className=\"w-full sm:w-auto\">\n              <RefreshCw className=\"w-4 h-4 mr-2\" />\n              <span className=\"sm:hidden\">Reset</span>\n              <span className=\"hidden sm:inline\">Reset to Default</span>\n            </Button>\n            <Button disabled={loading} className=\"w-full sm:w-auto\">\n              <Save className=\"w-4 h-4 mr-2\" />\n              <span className=\"sm:hidden\">Save All</span>\n              <span className=\"hidden sm:inline\">Save All Changes</span>\n            </Button>\n          </div>\n        </div>\n\n        {/* Tabs */}\n        <div className=\"border-b border-gray-200\">\n          <nav className=\"-mb-px flex flex-wrap gap-2 sm:gap-8\">\n            <button\n              onClick={() => setActiveTab('general')}\n              className={`py-2 px-1 border-b-2 font-medium text-xs sm:text-sm min-h-[44px] flex items-center ${\n                activeTab === 'general'\n                  ? 'border-blue-500 text-blue-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n              }`}\n            >\n              <School className=\"inline w-4 h-4 mr-1 sm:mr-2\" />\n              <span className=\"hidden sm:inline\">General Settings</span>\n              <span className=\"sm:hidden\">General</span>\n            </button>\n            <button\n              onClick={() => setActiveTab('academic')}\n              className={`py-2 px-1 border-b-2 font-medium text-xs sm:text-sm min-h-[44px] flex items-center ${\n                activeTab === 'academic'\n                  ? 'border-blue-500 text-blue-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n              }`}\n            >\n              <Calendar className=\"inline w-4 h-4 mr-1 sm:mr-2\" />\n              <span className=\"hidden sm:inline\">Academic Settings</span>\n              <span className=\"sm:hidden\">Academic</span>\n            </button>\n            <button\n              onClick={() => setActiveTab('notifications')}\n              className={`py-2 px-1 border-b-2 font-medium text-xs sm:text-sm min-h-[44px] flex items-center ${\n                activeTab === 'notifications'\n                  ? 'border-blue-500 text-blue-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n              }`}\n            >\n              <Bell className=\"inline w-4 h-4 mr-1 sm:mr-2\" />\n              <span className=\"hidden sm:inline\">Notifications</span>\n              <span className=\"sm:hidden\">Alerts</span>\n            </button>\n            <button\n              onClick={() => setActiveTab('security')}\n              className={`py-2 px-1 border-b-2 font-medium text-xs sm:text-sm min-h-[44px] flex items-center ${\n                activeTab === 'security'\n                  ? 'border-blue-500 text-blue-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n              }`}\n            >\n              <Shield className=\"inline w-4 h-4 mr-1 sm:mr-2\" />\n              <span className=\"hidden sm:inline\">Security</span>\n              <span className=\"sm:hidden\">Security</span>\n            </button>\n          </nav>\n        </div>\n\n        {/* General Settings Tab */}\n        {activeTab === 'general' && (\n          <div className=\"space-y-6\">\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center\">\n                  <School className=\"w-5 h-5 mr-2\" />\n                  School Information\n                </CardTitle>\n                <CardDescription>\n                  Update basic school information and contact details\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <Label htmlFor=\"schoolName\">School Name</Label>\n                    <Input\n                      id=\"schoolName\"\n                      value={schoolInfo.name}\n                      onChange={(e) => setSchoolInfo({...schoolInfo, name: e.target.value})}\n                      placeholder=\"Enter school name\"\n                    />\n                  </div>\n                  <div>\n                    <Label htmlFor=\"principal\">Principal Name</Label>\n                    <Input\n                      id=\"principal\"\n                      value={schoolInfo.principal}\n                      onChange={(e) => setSchoolInfo({...schoolInfo, principal: e.target.value})}\n                      placeholder=\"Enter principal name\"\n                    />\n                  </div>\n                  <div>\n                    <Label htmlFor=\"phone\">Phone Number</Label>\n                    <Input\n                      id=\"phone\"\n                      value={schoolInfo.phone}\n                      onChange={(e) => setSchoolInfo({...schoolInfo, phone: e.target.value})}\n                      placeholder=\"Enter phone number\"\n                    />\n                  </div>\n                  <div>\n                    <Label htmlFor=\"email\">Email Address</Label>\n                    <Input\n                      id=\"email\"\n                      type=\"email\"\n                      value={schoolInfo.email}\n                      onChange={(e) => setSchoolInfo({...schoolInfo, email: e.target.value})}\n                      placeholder=\"Enter email address\"\n                    />\n                  </div>\n                  <div>\n                    <Label htmlFor=\"website\">Website</Label>\n                    <Input\n                      id=\"website\"\n                      value={schoolInfo.website}\n                      onChange={(e) => setSchoolInfo({...schoolInfo, website: e.target.value})}\n                      placeholder=\"Enter website URL\"\n                    />\n                  </div>\n                  <div>\n                    <Label htmlFor=\"establishedYear\">Established Year</Label>\n                    <Input\n                      id=\"establishedYear\"\n                      value={schoolInfo.establishedYear}\n                      onChange={(e) => setSchoolInfo({...schoolInfo, establishedYear: e.target.value})}\n                      placeholder=\"Enter established year\"\n                    />\n                  </div>\n                  <div className=\"md:col-span-2\">\n                    <Label htmlFor=\"address\">Address</Label>\n                    <Input\n                      id=\"address\"\n                      value={schoolInfo.address}\n                      onChange={(e) => setSchoolInfo({...schoolInfo, address: e.target.value})}\n                      placeholder=\"Enter complete address\"\n                    />\n                  </div>\n                </div>\n                <div className=\"mt-6\">\n                  <Button onClick={handleSaveSchoolInfo} disabled={loading}>\n                    <Save className=\"w-4 h-4 mr-2\" />\n                    {loading ? 'Saving...' : 'Save School Information'}\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        )}\n\n        {/* Academic Settings Tab */}\n        {activeTab === 'academic' && (\n          <div className=\"space-y-6\">\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center\">\n                  <Calendar className=\"w-5 h-5 mr-2\" />\n                  Academic Configuration\n                </CardTitle>\n                <CardDescription>\n                  Configure academic year, grading system, and performance criteria\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <Label htmlFor=\"academicYear\">Academic Year</Label>\n                    <Input\n                      id=\"academicYear\"\n                      value={academicSettings.academicYear}\n                      onChange={(e) => setAcademicSettings({...academicSettings, academicYear: e.target.value})}\n                      placeholder=\"e.g., 2024-2025\"\n                    />\n                  </div>\n                  <div>\n                    <Label htmlFor=\"currentTerm\">Current Term</Label>\n                    <select\n                      id=\"currentTerm\"\n                      value={academicSettings.currentTerm}\n                      onChange={(e) => setAcademicSettings({...academicSettings, currentTerm: e.target.value})}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    >\n                      <option value=\"Term 1\">Term 1</option>\n                      <option value=\"Term 2\">Term 2</option>\n                      <option value=\"Term 3\">Term 3</option>\n                    </select>\n                  </div>\n                  <div>\n                    <Label htmlFor=\"gradingSystem\">Grading System</Label>\n                    <select\n                      id=\"gradingSystem\"\n                      value={academicSettings.gradingSystem}\n                      onChange={(e) => setAcademicSettings({...academicSettings, gradingSystem: e.target.value as any})}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    >\n                      <option value=\"LETTER\">Letter Grades (A+, A, B+, B, etc.)</option>\n                      <option value=\"PERCENTAGE\">Percentage</option>\n                      <option value=\"NUMERIC\">Numeric (1-10)</option>\n                    </select>\n                  </div>\n                  <div>\n                    <Label htmlFor=\"passPercentage\">Pass Percentage</Label>\n                    <Input\n                      id=\"passPercentage\"\n                      type=\"number\"\n                      value={academicSettings.passPercentage}\n                      onChange={(e) => setAcademicSettings({...academicSettings, passPercentage: parseInt(e.target.value)})}\n                      placeholder=\"40\"\n                    />\n                  </div>\n                  <div>\n                    <Label htmlFor=\"maxAttendancePercentage\">Minimum Attendance %</Label>\n                    <Input\n                      id=\"maxAttendancePercentage\"\n                      type=\"number\"\n                      value={academicSettings.maxAttendancePercentage}\n                      onChange={(e) => setAcademicSettings({...academicSettings, maxAttendancePercentage: parseInt(e.target.value)})}\n                      placeholder=\"75\"\n                    />\n                  </div>\n                </div>\n                <div className=\"mt-6\">\n                  <Button onClick={handleSaveAcademicSettings} disabled={loading}>\n                    <Save className=\"w-4 h-4 mr-2\" />\n                    {loading ? 'Saving...' : 'Save Academic Settings'}\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        )}\n\n        {/* Notifications Tab */}\n        {activeTab === 'notifications' && (\n          <div className=\"space-y-6\">\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center\">\n                  <Bell className=\"w-5 h-5 mr-2\" />\n                  Notification Settings\n                </CardTitle>\n                <CardDescription>\n                  Configure email notifications and alerts\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <h4 className=\"font-medium\">Attendance Alerts</h4>\n                      <p className=\"text-sm text-gray-600\">Send notifications for low attendance</p>\n                    </div>\n                    <input \n                      type=\"checkbox\" \n                      className=\"rounded\" \n                      checked={notificationSettings.attendanceAlerts}\n                      onChange={(e) => setNotificationSettings({\n                        ...notificationSettings,\n                        attendanceAlerts: e.target.checked\n                      })}\n                    />\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <h4 className=\"font-medium\">Exam Results</h4>\n                      <p className=\"text-sm text-gray-600\">Notify when exam results are published</p>\n                    </div>\n                    <input \n                      type=\"checkbox\" \n                      className=\"rounded\" \n                      checked={notificationSettings.examResults}\n                      onChange={(e) => setNotificationSettings({\n                        ...notificationSettings,\n                        examResults: e.target.checked\n                      })}\n                    />\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <h4 className=\"font-medium\">Report Card Generation</h4>\n                      <p className=\"text-sm text-gray-600\">Notify when report cards are ready</p>\n                    </div>\n                    <input \n                      type=\"checkbox\" \n                      className=\"rounded\" \n                      checked={notificationSettings.reportCardGeneration}\n                      onChange={(e) => setNotificationSettings({\n                        ...notificationSettings,\n                        reportCardGeneration: e.target.checked\n                      })}\n                    />\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <h4 className=\"font-medium\">System Updates</h4>\n                      <p className=\"text-sm text-gray-600\">Receive system maintenance notifications</p>\n                    </div>\n                    <input \n                      type=\"checkbox\" \n                      className=\"rounded\" \n                      checked={notificationSettings.systemUpdates}\n                      onChange={(e) => setNotificationSettings({\n                        ...notificationSettings,\n                        systemUpdates: e.target.checked\n                      })}\n                    />\n                  </div>\n                </div>\n                <div className=\"mt-6\">\n                  <Button onClick={handleSaveNotificationSettings} disabled={loading}>\n                    <Save className=\"w-4 h-4 mr-2\" />\n                    {loading ? 'Saving...' : 'Save Notification Settings'}\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        )}\n\n        {/* Security Tab */}\n        {activeTab === 'security' && (\n          <div className=\"space-y-6\">\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center\">\n                  <Shield className=\"w-5 h-5 mr-2\" />\n                  Security Settings\n                </CardTitle>\n                <CardDescription>\n                  Manage password policies and security settings\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  <div>\n                    <Label htmlFor=\"sessionTimeout\">Session Timeout (minutes)</Label>\n                    <Input\n                      id=\"sessionTimeout\"\n                      type=\"number\"\n                      value={securitySettings.sessionTimeout}\n                      onChange={(e) => setSecuritySettings({\n                        ...securitySettings,\n                        sessionTimeout: parseInt(e.target.value)\n                      })}\n                      placeholder=\"30\"\n                    />\n                  </div>\n                  <div>\n                    <Label htmlFor=\"passwordPolicy\">Password Policy</Label>\n                    <select\n                      id=\"passwordPolicy\"\n                      value={securitySettings.passwordPolicy}\n                      onChange={(e) => setSecuritySettings({\n                        ...securitySettings,\n                        passwordPolicy: e.target.value\n                      })}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    >\n                      <option value=\"strong\">Strong (8+ chars, mixed case, numbers)</option>\n                      <option value=\"medium\">Medium (6+ chars, mixed case)</option>\n                      <option value=\"weak\">Weak (4+ chars)</option>\n                    </select>\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <h4 className=\"font-medium\">Two-Factor Authentication</h4>\n                      <p className=\"text-sm text-gray-600\">Require 2FA for admin accounts</p>\n                    </div>\n                    <input \n                      type=\"checkbox\" \n                      className=\"rounded\" \n                      checked={securitySettings.twoFactorAuth}\n                      onChange={(e) => setSecuritySettings({\n                        ...securitySettings,\n                        twoFactorAuth: e.target.checked\n                      })}\n                    />\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <h4 className=\"font-medium\">Login Attempts</h4>\n                      <p className=\"text-sm text-gray-600\">Lock account after failed attempts</p>\n                    </div>\n                    <input \n                      type=\"checkbox\" \n                      className=\"rounded\" \n                      checked={securitySettings.loginAttempts}\n                      onChange={(e) => setSecuritySettings({\n                        ...securitySettings,\n                        loginAttempts: e.target.checked\n                      })}\n                    />\n                  </div>\n                </div>\n                <div className=\"mt-6\">\n                  <Button onClick={handleSaveSecuritySettings} disabled={loading}>\n                    <Save className=\"w-4 h-4 mr-2\" />\n                    {loading ? 'Saving...' : 'Save Security Settings'}\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center\">\n                  <Database className=\"w-5 h-5 mr-2\" />\n                  System Maintenance\n                </CardTitle>\n                <CardDescription>\n                  Database backup and system maintenance options\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  <Button variant=\"outline\" className=\"w-full\">\n                    <Database className=\"w-4 h-4 mr-2\" />\n                    Create Database Backup\n                  </Button>\n                  <Button variant=\"outline\" className=\"w-full\">\n                    <RefreshCw className=\"w-4 h-4 mr-2\" />\n                    Clear Cache\n                  </Button>\n                  <Button variant=\"outline\" className=\"w-full\">\n                    <Mail className=\"w-4 h-4 mr-2\" />\n                    Test Email Configuration\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        )}\n      </div>\n    </DashboardLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA+CA;AAvDA;;;;;;;;;;AAyDe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,kWAAQ,EAAwD;IAClG,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,kWAAQ,EAAC;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,kWAAQ,EAAqD;IAE3F,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,kWAAQ,EAAa;QACvD,MAAM;QACN,SAAS;QACT,OAAO;QACP,OAAO;QACP,SAAS;QACT,WAAW;QACX,iBAAiB;IACnB;IACA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,kWAAQ,EAAmB;QACzE,cAAc;QACd,aAAa;QACb,eAAe;QACf,gBAAgB;QAChB,yBAAyB;IAC3B;IACA,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,IAAA,kWAAQ,EAAuB;QACrF,kBAAkB;QAClB,aAAa;QACb,sBAAsB;QACtB,eAAe;IACjB;IACA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,kWAAQ,EAAmB;QACzE,gBAAgB;QAChB,gBAAgB;QAChB,eAAe;QACf,eAAe;IACjB;IAEA,yBAAyB;IACzB,IAAA,mWAAS,EAAC;QACR,MAAM,eAAe;YACnB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM;gBAC7B,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,IAAI,KAAK,OAAO,EAAE;wBAChB,cAAc;4BACZ,MAAM,KAAK,OAAO,CAAC,UAAU;4BAC7B,SAAS,KAAK,OAAO,CAAC,OAAO;4BAC7B,OAAO,KAAK,OAAO,CAAC,KAAK;4BACzB,OAAO,KAAK,OAAO,CAAC,KAAK;4BACzB,SAAS,KAAK,OAAO,CAAC,OAAO;4BAC7B,WAAW,KAAK,OAAO,CAAC,SAAS;4BACjC,iBAAiB,KAAK,OAAO,CAAC,eAAe;wBAC/C;oBACF;oBACA,IAAI,KAAK,QAAQ,EAAE;wBACjB,oBAAoB;4BAClB,cAAc,KAAK,QAAQ,CAAC,YAAY;4BACxC,aAAa,KAAK,QAAQ,CAAC,WAAW;4BACtC,eAAe,KAAK,QAAQ,CAAC,aAAa;4BAC1C,gBAAgB,KAAK,QAAQ,CAAC,cAAc;4BAC5C,yBAAyB,KAAK,QAAQ,CAAC,uBAAuB;wBAChE;oBACF;oBACA,IAAI,KAAK,aAAa,EAAE;wBACtB,wBAAwB;4BACtB,kBAAkB,KAAK,aAAa,CAAC,gBAAgB;4BACrD,aAAa,KAAK,aAAa,CAAC,WAAW;4BAC3C,sBAAsB,KAAK,aAAa,CAAC,oBAAoB;4BAC7D,eAAe,KAAK,aAAa,CAAC,aAAa;wBACjD;oBACF;oBACA,IAAI,KAAK,QAAQ,EAAE;wBACjB,oBAAoB;4BAClB,gBAAgB,KAAK,QAAQ,CAAC,cAAc;4BAC5C,gBAAgB,KAAK,QAAQ,CAAC,cAAc;4BAC5C,eAAe,KAAK,QAAQ,CAAC,aAAa;4BAC1C,eAAe,KAAK,QAAQ,CAAC,aAAa;wBAC5C;oBACF;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;YAC3C;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,cAAc,CAAC,MAA2B;QAC9C,WAAW;YAAE;YAAM;QAAK;QACxB,WAAW,IAAM,WAAW,OAAO;IACrC;IAEA,MAAM,uBAAuB;QAC3B,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,uBAAuB;gBAClD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,MAAM;oBAAW,MAAM;gBAAW;YAC3D;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,YAAY,WAAW;YACzB,OAAO;gBACL,YAAY,SAAS;YACvB;QACF,EAAE,OAAO,OAAO;YACd,YAAY,SAAS;QACvB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,6BAA6B;QACjC,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,uBAAuB;gBAClD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,MAAM;oBAAY,MAAM;gBAAiB;YAClE;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,YAAY,WAAW;YACzB,OAAO;gBACL,YAAY,SAAS;YACvB;QACF,EAAE,OAAO,OAAO;YACd,YAAY,SAAS;QACvB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iCAAiC;QACrC,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,uBAAuB;gBAClD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,MAAM;oBAAiB,MAAM;gBAAqB;YAC3E;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,YAAY,WAAW;YACzB,OAAO;gBACL,YAAY,SAAS;YACvB;QACF,EAAE,OAAO,OAAO;YACd,YAAY,SAAS;QACvB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,6BAA6B;QACjC,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,uBAAuB;gBAClD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,MAAM;oBAAY,MAAM;gBAAiB;YAClE;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,YAAY,WAAW;YACzB,OAAO;gBACL,YAAY,SAAS;YACvB;QACF,EAAE,OAAO,OAAO;YACd,YAAY,SAAS;QACvB,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,+XAAC,gMAAe;QAAC,OAAM;QAAkB,YAAY,6KAAe;kBAClE,cAAA,+XAAC;YAAI,WAAU;;gBAEZ,yBACC,+XAAC;oBAAI,WAAW,CAAC,iCAAiC,EAChD,QAAQ,IAAI,KAAK,YACb,uDACA,gDACJ;;wBACC,QAAQ,IAAI,KAAK,0BAChB,+XAAC,gWAAW;4BAAC,WAAU;;;;;iDAEvB,+XAAC,yVAAW;4BAAC,WAAU;;;;;;wBAExB,QAAQ,IAAI;;;;;;;8BAKjB,+XAAC;oBAAI,WAAU;;sCACb,+XAAC;;8CACC,+XAAC;oCAAG,WAAU;8CAA8C;;;;;;8CAC5D,+XAAC;oCAAE,WAAU;8CAAqC;;;;;;;;;;;;sCAEpD,+XAAC;4BAAI,WAAU;;8CACb,+XAAC,8KAAM;oCAAC,SAAQ;oCAAU,UAAU;oCAAS,WAAU;;sDACrD,+XAAC,mVAAS;4CAAC,WAAU;;;;;;sDACrB,+XAAC;4CAAK,WAAU;sDAAY;;;;;;sDAC5B,+XAAC;4CAAK,WAAU;sDAAmB;;;;;;;;;;;;8CAErC,+XAAC,8KAAM;oCAAC,UAAU;oCAAS,WAAU;;sDACnC,+XAAC,gUAAI;4CAAC,WAAU;;;;;;sDAChB,+XAAC;4CAAK,WAAU;sDAAY;;;;;;sDAC5B,+XAAC;4CAAK,WAAU;sDAAmB;;;;;;;;;;;;;;;;;;;;;;;;8BAMzC,+XAAC;oBAAI,WAAU;8BACb,cAAA,+XAAC;wBAAI,WAAU;;0CACb,+XAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,mFAAmF,EAC7F,cAAc,YACV,kCACA,8EACJ;;kDAEF,+XAAC,sUAAM;wCAAC,WAAU;;;;;;kDAClB,+XAAC;wCAAK,WAAU;kDAAmB;;;;;;kDACnC,+XAAC;wCAAK,WAAU;kDAAY;;;;;;;;;;;;0CAE9B,+XAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,mFAAmF,EAC7F,cAAc,aACV,kCACA,8EACJ;;kDAEF,+XAAC,4UAAQ;wCAAC,WAAU;;;;;;kDACpB,+XAAC;wCAAK,WAAU;kDAAmB;;;;;;kDACnC,+XAAC;wCAAK,WAAU;kDAAY;;;;;;;;;;;;0CAE9B,+XAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,mFAAmF,EAC7F,cAAc,kBACV,kCACA,8EACJ;;kDAEF,+XAAC,gUAAI;wCAAC,WAAU;;;;;;kDAChB,+XAAC;wCAAK,WAAU;kDAAmB;;;;;;kDACnC,+XAAC;wCAAK,WAAU;kDAAY;;;;;;;;;;;;0CAE9B,+XAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,mFAAmF,EAC7F,cAAc,aACV,kCACA,8EACJ;;kDAEF,+XAAC,sUAAM;wCAAC,WAAU;;;;;;kDAClB,+XAAC;wCAAK,WAAU;kDAAmB;;;;;;kDACnC,+XAAC;wCAAK,WAAU;kDAAY;;;;;;;;;;;;;;;;;;;;;;;gBAMjC,cAAc,2BACb,+XAAC;oBAAI,WAAU;8BACb,cAAA,+XAAC,0KAAI;;0CACH,+XAAC,gLAAU;;kDACT,+XAAC,+KAAS;wCAAC,WAAU;;0DACnB,+XAAC,sUAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGrC,+XAAC,qLAAe;kDAAC;;;;;;;;;;;;0CAInB,+XAAC,iLAAW;;kDACV,+XAAC;wCAAI,WAAU;;0DACb,+XAAC;;kEACC,+XAAC,4KAAK;wDAAC,SAAQ;kEAAa;;;;;;kEAC5B,+XAAC,4KAAK;wDACJ,IAAG;wDACH,OAAO,WAAW,IAAI;wDACtB,UAAU,CAAC,IAAM,cAAc;gEAAC,GAAG,UAAU;gEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4DAAA;wDACnE,aAAY;;;;;;;;;;;;0DAGhB,+XAAC;;kEACC,+XAAC,4KAAK;wDAAC,SAAQ;kEAAY;;;;;;kEAC3B,+XAAC,4KAAK;wDACJ,IAAG;wDACH,OAAO,WAAW,SAAS;wDAC3B,UAAU,CAAC,IAAM,cAAc;gEAAC,GAAG,UAAU;gEAAE,WAAW,EAAE,MAAM,CAAC,KAAK;4DAAA;wDACxE,aAAY;;;;;;;;;;;;0DAGhB,+XAAC;;kEACC,+XAAC,4KAAK;wDAAC,SAAQ;kEAAQ;;;;;;kEACvB,+XAAC,4KAAK;wDACJ,IAAG;wDACH,OAAO,WAAW,KAAK;wDACvB,UAAU,CAAC,IAAM,cAAc;gEAAC,GAAG,UAAU;gEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4DAAA;wDACpE,aAAY;;;;;;;;;;;;0DAGhB,+XAAC;;kEACC,+XAAC,4KAAK;wDAAC,SAAQ;kEAAQ;;;;;;kEACvB,+XAAC,4KAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,OAAO,WAAW,KAAK;wDACvB,UAAU,CAAC,IAAM,cAAc;gEAAC,GAAG,UAAU;gEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4DAAA;wDACpE,aAAY;;;;;;;;;;;;0DAGhB,+XAAC;;kEACC,+XAAC,4KAAK;wDAAC,SAAQ;kEAAU;;;;;;kEACzB,+XAAC,4KAAK;wDACJ,IAAG;wDACH,OAAO,WAAW,OAAO;wDACzB,UAAU,CAAC,IAAM,cAAc;gEAAC,GAAG,UAAU;gEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;4DAAA;wDACtE,aAAY;;;;;;;;;;;;0DAGhB,+XAAC;;kEACC,+XAAC,4KAAK;wDAAC,SAAQ;kEAAkB;;;;;;kEACjC,+XAAC,4KAAK;wDACJ,IAAG;wDACH,OAAO,WAAW,eAAe;wDACjC,UAAU,CAAC,IAAM,cAAc;gEAAC,GAAG,UAAU;gEAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;4DAAA;wDAC9E,aAAY;;;;;;;;;;;;0DAGhB,+XAAC;gDAAI,WAAU;;kEACb,+XAAC,4KAAK;wDAAC,SAAQ;kEAAU;;;;;;kEACzB,+XAAC,4KAAK;wDACJ,IAAG;wDACH,OAAO,WAAW,OAAO;wDACzB,UAAU,CAAC,IAAM,cAAc;gEAAC,GAAG,UAAU;gEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;4DAAA;wDACtE,aAAY;;;;;;;;;;;;;;;;;;kDAIlB,+XAAC;wCAAI,WAAU;kDACb,cAAA,+XAAC,8KAAM;4CAAC,SAAS;4CAAsB,UAAU;;8DAC/C,+XAAC,gUAAI;oDAAC,WAAU;;;;;;gDACf,UAAU,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBASpC,cAAc,4BACb,+XAAC;oBAAI,WAAU;8BACb,cAAA,+XAAC,0KAAI;;0CACH,+XAAC,gLAAU;;kDACT,+XAAC,+KAAS;wCAAC,WAAU;;0DACnB,+XAAC,4UAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGvC,+XAAC,qLAAe;kDAAC;;;;;;;;;;;;0CAInB,+XAAC,iLAAW;;kDACV,+XAAC;wCAAI,WAAU;;0DACb,+XAAC;;kEACC,+XAAC,4KAAK;wDAAC,SAAQ;kEAAe;;;;;;kEAC9B,+XAAC,4KAAK;wDACJ,IAAG;wDACH,OAAO,iBAAiB,YAAY;wDACpC,UAAU,CAAC,IAAM,oBAAoB;gEAAC,GAAG,gBAAgB;gEAAE,cAAc,EAAE,MAAM,CAAC,KAAK;4DAAA;wDACvF,aAAY;;;;;;;;;;;;0DAGhB,+XAAC;;kEACC,+XAAC,4KAAK;wDAAC,SAAQ;kEAAc;;;;;;kEAC7B,+XAAC;wDACC,IAAG;wDACH,OAAO,iBAAiB,WAAW;wDACnC,UAAU,CAAC,IAAM,oBAAoB;gEAAC,GAAG,gBAAgB;gEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4DAAA;wDACtF,WAAU;;0EAEV,+XAAC;gEAAO,OAAM;0EAAS;;;;;;0EACvB,+XAAC;gEAAO,OAAM;0EAAS;;;;;;0EACvB,+XAAC;gEAAO,OAAM;0EAAS;;;;;;;;;;;;;;;;;;0DAG3B,+XAAC;;kEACC,+XAAC,4KAAK;wDAAC,SAAQ;kEAAgB;;;;;;kEAC/B,+XAAC;wDACC,IAAG;wDACH,OAAO,iBAAiB,aAAa;wDACrC,UAAU,CAAC,IAAM,oBAAoB;gEAAC,GAAG,gBAAgB;gEAAE,eAAe,EAAE,MAAM,CAAC,KAAK;4DAAO;wDAC/F,WAAU;;0EAEV,+XAAC;gEAAO,OAAM;0EAAS;;;;;;0EACvB,+XAAC;gEAAO,OAAM;0EAAa;;;;;;0EAC3B,+XAAC;gEAAO,OAAM;0EAAU;;;;;;;;;;;;;;;;;;0DAG5B,+XAAC;;kEACC,+XAAC,4KAAK;wDAAC,SAAQ;kEAAiB;;;;;;kEAChC,+XAAC,4KAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,OAAO,iBAAiB,cAAc;wDACtC,UAAU,CAAC,IAAM,oBAAoB;gEAAC,GAAG,gBAAgB;gEAAE,gBAAgB,SAAS,EAAE,MAAM,CAAC,KAAK;4DAAC;wDACnG,aAAY;;;;;;;;;;;;0DAGhB,+XAAC;;kEACC,+XAAC,4KAAK;wDAAC,SAAQ;kEAA0B;;;;;;kEACzC,+XAAC,4KAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,OAAO,iBAAiB,uBAAuB;wDAC/C,UAAU,CAAC,IAAM,oBAAoB;gEAAC,GAAG,gBAAgB;gEAAE,yBAAyB,SAAS,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAC5G,aAAY;;;;;;;;;;;;;;;;;;kDAIlB,+XAAC;wCAAI,WAAU;kDACb,cAAA,+XAAC,8KAAM;4CAAC,SAAS;4CAA4B,UAAU;;8DACrD,+XAAC,gUAAI;oDAAC,WAAU;;;;;;gDACf,UAAU,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBASpC,cAAc,iCACb,+XAAC;oBAAI,WAAU;8BACb,cAAA,+XAAC,0KAAI;;0CACH,+XAAC,gLAAU;;kDACT,+XAAC,+KAAS;wCAAC,WAAU;;0DACnB,+XAAC,gUAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGnC,+XAAC,qLAAe;kDAAC;;;;;;;;;;;;0CAInB,+XAAC,iLAAW;;kDACV,+XAAC;wCAAI,WAAU;;0DACb,+XAAC;gDAAI,WAAU;;kEACb,+XAAC;;0EACC,+XAAC;gEAAG,WAAU;0EAAc;;;;;;0EAC5B,+XAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,+XAAC;wDACC,MAAK;wDACL,WAAU;wDACV,SAAS,qBAAqB,gBAAgB;wDAC9C,UAAU,CAAC,IAAM,wBAAwB;gEACvC,GAAG,oBAAoB;gEACvB,kBAAkB,EAAE,MAAM,CAAC,OAAO;4DACpC;;;;;;;;;;;;0DAGJ,+XAAC;gDAAI,WAAU;;kEACb,+XAAC;;0EACC,+XAAC;gEAAG,WAAU;0EAAc;;;;;;0EAC5B,+XAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,+XAAC;wDACC,MAAK;wDACL,WAAU;wDACV,SAAS,qBAAqB,WAAW;wDACzC,UAAU,CAAC,IAAM,wBAAwB;gEACvC,GAAG,oBAAoB;gEACvB,aAAa,EAAE,MAAM,CAAC,OAAO;4DAC/B;;;;;;;;;;;;0DAGJ,+XAAC;gDAAI,WAAU;;kEACb,+XAAC;;0EACC,+XAAC;gEAAG,WAAU;0EAAc;;;;;;0EAC5B,+XAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,+XAAC;wDACC,MAAK;wDACL,WAAU;wDACV,SAAS,qBAAqB,oBAAoB;wDAClD,UAAU,CAAC,IAAM,wBAAwB;gEACvC,GAAG,oBAAoB;gEACvB,sBAAsB,EAAE,MAAM,CAAC,OAAO;4DACxC;;;;;;;;;;;;0DAGJ,+XAAC;gDAAI,WAAU;;kEACb,+XAAC;;0EACC,+XAAC;gEAAG,WAAU;0EAAc;;;;;;0EAC5B,+XAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,+XAAC;wDACC,MAAK;wDACL,WAAU;wDACV,SAAS,qBAAqB,aAAa;wDAC3C,UAAU,CAAC,IAAM,wBAAwB;gEACvC,GAAG,oBAAoB;gEACvB,eAAe,EAAE,MAAM,CAAC,OAAO;4DACjC;;;;;;;;;;;;;;;;;;kDAIN,+XAAC;wCAAI,WAAU;kDACb,cAAA,+XAAC,8KAAM;4CAAC,SAAS;4CAAgC,UAAU;;8DACzD,+XAAC,gUAAI;oDAAC,WAAU;;;;;;gDACf,UAAU,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBASpC,cAAc,4BACb,+XAAC;oBAAI,WAAU;;sCACb,+XAAC,0KAAI;;8CACH,+XAAC,gLAAU;;sDACT,+XAAC,+KAAS;4CAAC,WAAU;;8DACnB,+XAAC,sUAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGrC,+XAAC,qLAAe;sDAAC;;;;;;;;;;;;8CAInB,+XAAC,iLAAW;;sDACV,+XAAC;4CAAI,WAAU;;8DACb,+XAAC;;sEACC,+XAAC,4KAAK;4DAAC,SAAQ;sEAAiB;;;;;;sEAChC,+XAAC,4KAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,OAAO,iBAAiB,cAAc;4DACtC,UAAU,CAAC,IAAM,oBAAoB;oEACnC,GAAG,gBAAgB;oEACnB,gBAAgB,SAAS,EAAE,MAAM,CAAC,KAAK;gEACzC;4DACA,aAAY;;;;;;;;;;;;8DAGhB,+XAAC;;sEACC,+XAAC,4KAAK;4DAAC,SAAQ;sEAAiB;;;;;;sEAChC,+XAAC;4DACC,IAAG;4DACH,OAAO,iBAAiB,cAAc;4DACtC,UAAU,CAAC,IAAM,oBAAoB;oEACnC,GAAG,gBAAgB;oEACnB,gBAAgB,EAAE,MAAM,CAAC,KAAK;gEAChC;4DACA,WAAU;;8EAEV,+XAAC;oEAAO,OAAM;8EAAS;;;;;;8EACvB,+XAAC;oEAAO,OAAM;8EAAS;;;;;;8EACvB,+XAAC;oEAAO,OAAM;8EAAO;;;;;;;;;;;;;;;;;;8DAGzB,+XAAC;oDAAI,WAAU;;sEACb,+XAAC;;8EACC,+XAAC;oEAAG,WAAU;8EAAc;;;;;;8EAC5B,+XAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAEvC,+XAAC;4DACC,MAAK;4DACL,WAAU;4DACV,SAAS,iBAAiB,aAAa;4DACvC,UAAU,CAAC,IAAM,oBAAoB;oEACnC,GAAG,gBAAgB;oEACnB,eAAe,EAAE,MAAM,CAAC,OAAO;gEACjC;;;;;;;;;;;;8DAGJ,+XAAC;oDAAI,WAAU;;sEACb,+XAAC;;8EACC,+XAAC;oEAAG,WAAU;8EAAc;;;;;;8EAC5B,+XAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAEvC,+XAAC;4DACC,MAAK;4DACL,WAAU;4DACV,SAAS,iBAAiB,aAAa;4DACvC,UAAU,CAAC,IAAM,oBAAoB;oEACnC,GAAG,gBAAgB;oEACnB,eAAe,EAAE,MAAM,CAAC,OAAO;gEACjC;;;;;;;;;;;;;;;;;;sDAIN,+XAAC;4CAAI,WAAU;sDACb,cAAA,+XAAC,8KAAM;gDAAC,SAAS;gDAA4B,UAAU;;kEACrD,+XAAC,gUAAI;wDAAC,WAAU;;;;;;oDACf,UAAU,cAAc;;;;;;;;;;;;;;;;;;;;;;;;sCAMjC,+XAAC,0KAAI;;8CACH,+XAAC,gLAAU;;sDACT,+XAAC,+KAAS;4CAAC,WAAU;;8DACnB,+XAAC,4UAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGvC,+XAAC,qLAAe;sDAAC;;;;;;;;;;;;8CAInB,+XAAC,iLAAW;8CACV,cAAA,+XAAC;wCAAI,WAAU;;0DACb,+XAAC,8KAAM;gDAAC,SAAQ;gDAAU,WAAU;;kEAClC,+XAAC,4UAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGvC,+XAAC,8KAAM;gDAAC,SAAQ;gDAAU,WAAU;;kEAClC,+XAAC,mVAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGxC,+XAAC,8KAAM;gDAAC,SAAQ;gDAAU,WAAU;;kEAClC,+XAAC,gUAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWrD", "debugId": null}}]}