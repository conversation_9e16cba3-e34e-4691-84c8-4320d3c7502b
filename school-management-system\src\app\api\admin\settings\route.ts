import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type')

    // Get all settings
    const settings = await prisma.setting.findMany()

    // Convert settings array to object for easier access
    const settingsObject = settings.reduce((acc, setting) => {
      acc[setting.key] = setting.value
      return acc
    }, {} as Record<string, string>)

    // Return different data based on type parameter
    switch (type) {
      case 'general':
        return NextResponse.json({
          schoolName: settingsObject['school_name'] || 'Advance School',
          address: settingsObject['school_address'] || '123 Education Street, City, State 12345',
          phone: settingsObject['school_phone'] || '+****************',
          email: settingsObject['school_email'] || '<EMAIL>',
          website: settingsObject['school_website'] || 'www.advanceschool.edu',
          principal: settingsObject['school_principal'] || 'Dr. John Smith',
          establishedYear: settingsObject['school_established_year'] || '1995'
        })
      
      case 'academic':
        return NextResponse.json({
          academicYear: settingsObject['academic_year'] || '2024-2025',
          currentTerm: settingsObject['current_term'] || 'Term 1',
          gradingSystem: settingsObject['grading_system'] || 'LETTER',
          passPercentage: parseInt(settingsObject['pass_percentage']) || 40,
          maxAttendancePercentage: parseInt(settingsObject['max_attendance_percentage']) || 75
        })
      
      case 'notifications':
        return NextResponse.json({
          attendanceAlerts: settingsObject['attendance_alerts'] === 'true',
          examResults: settingsObject['exam_results'] === 'true',
          reportCardGeneration: settingsObject['report_card_generation'] === 'true',
          systemUpdates: settingsObject['system_updates'] === 'true'
        })
      
      case 'security':
        return NextResponse.json({
          sessionTimeout: parseInt(settingsObject['session_timeout']) || 30,
          passwordPolicy: settingsObject['password_policy'] || 'strong',
          twoFactorAuth: settingsObject['two_factor_auth'] === 'true',
          loginAttempts: settingsObject['login_attempts'] === 'true'
        })
      
      default:
        // Return all settings
        return NextResponse.json({
          general: {
            schoolName: settingsObject['school_name'] || 'Advance School',
            address: settingsObject['school_address'] || '123 Education Street, City, State 12345',
            phone: settingsObject['school_phone'] || '+****************',
            email: settingsObject['school_email'] || '<EMAIL>',
            website: settingsObject['school_website'] || 'www.advanceschool.edu',
            principal: settingsObject['school_principal'] || 'Dr. John Smith',
            establishedYear: settingsObject['school_established_year'] || '1995'
          },
          academic: {
            academicYear: settingsObject['academic_year'] || '2024-2025',
            currentTerm: settingsObject['current_term'] || 'Term 1',
            gradingSystem: settingsObject['grading_system'] || 'LETTER',
            passPercentage: parseInt(settingsObject['pass_percentage']) || 40,
            maxAttendancePercentage: parseInt(settingsObject['max_attendance_percentage']) || 75
          },
          notifications: {
            attendanceAlerts: settingsObject['attendance_alerts'] === 'true',
            examResults: settingsObject['exam_results'] === 'true',
            reportCardGeneration: settingsObject['report_card_generation'] === 'true',
            systemUpdates: settingsObject['system_updates'] === 'true'
          },
          security: {
            sessionTimeout: parseInt(settingsObject['session_timeout']) || 30,
            passwordPolicy: settingsObject['password_policy'] || 'strong',
            twoFactorAuth: settingsObject['two_factor_auth'] === 'true',
            loginAttempts: settingsObject['login_attempts'] === 'true'
          }
        })
    }
  } catch (error) {
    console.error('Error fetching settings:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { type, data } = body

    const settingsToUpdate: Array<{ key: string; value: string }> = []

    switch (type) {
      case 'general':
        settingsToUpdate.push(
          { key: 'school_name', value: data.schoolName },
          { key: 'school_address', value: data.address },
          { key: 'school_phone', value: data.phone },
          { key: 'school_email', value: data.email },
          { key: 'school_website', value: data.website },
          { key: 'school_principal', value: data.principal },
          { key: 'school_established_year', value: data.establishedYear }
        )
        break
      
      case 'academic':
        settingsToUpdate.push(
          { key: 'academic_year', value: data.academicYear },
          { key: 'current_term', value: data.currentTerm },
          { key: 'grading_system', value: data.gradingSystem },
          { key: 'pass_percentage', value: data.passPercentage.toString() },
          { key: 'max_attendance_percentage', value: data.maxAttendancePercentage.toString() }
        )
        break
      
      case 'notifications':
        settingsToUpdate.push(
          { key: 'attendance_alerts', value: data.attendanceAlerts.toString() },
          { key: 'exam_results', value: data.examResults.toString() },
          { key: 'report_card_generation', value: data.reportCardGeneration.toString() },
          { key: 'system_updates', value: data.systemUpdates.toString() }
        )
        break
      
      case 'security':
        settingsToUpdate.push(
          { key: 'session_timeout', value: data.sessionTimeout.toString() },
          { key: 'password_policy', value: data.passwordPolicy },
          { key: 'two_factor_auth', value: data.twoFactorAuth.toString() },
          { key: 'login_attempts', value: data.loginAttempts.toString() }
        )
        break
      
      default:
        return NextResponse.json({ error: 'Invalid settings type' }, { status: 400 })
    }

    // Update or create settings
    for (const setting of settingsToUpdate) {
      await prisma.setting.upsert({
        where: { key: setting.key },
        update: { value: setting.value },
        create: {
          key: setting.key,
          value: setting.value,
          category: type,
          updatedBy: session.user.id
        }
      })
    }

    // Log the settings update
    await prisma.auditLog.create({
      data: {
        action: 'SETTINGS_UPDATE',
        entityType: 'SETTING',
        entityId: type,
        userId: session.user.id,
        details: `Updated ${type} settings`,
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown'
      }
    })

    return NextResponse.json({ 
      message: `${type} settings updated successfully`,
      type,
      data 
    })
  } catch (error) {
    console.error('Error updating settings:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
